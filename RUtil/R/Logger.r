IsLoggerInited = function() {
    return(exists(".rutilLoggerInitialized", envir = .GlobalEnv, inherits = FALSE))
}

IsLoggerPathInited = function() {
    return(exists(".rutilLoggerPath", envir = .GlobalEnv, inherits = FALSE))
}

#' nodoc
#' @export
GetLoggerPath = function() {
    if (IsLoggerPathInited()) .rutilLoggerPath else NULL
}

################################################################################

#' nodoc
#' @export
GenLogPath = function(logRoot, name, tz = NULL) {
    time = Sys.time()
    if (!is.null(tz)) setattr(time, "tzone", tz)
    date = format(time, format = "%Y-%m-%d")

    datePath = file.path(logRoot, date)
    if (!dir.exists(datePath)) dir.create(datePath, FALSE, TRUE)
    existLog = list.files(datePath, pattern = paste0(name, "[0-9_]*", ".log"))
    newLogSuffix = ifelse(length(existLog) > 0, glue("_{length(existLog)}"), "")
    ret = file.path(datePath, glue("{name}{newLogSuffix}.log"))
    ret = normalizePath(ret, mustWork = FALSE)
    return(ret)
}


ColorizedByLogLevel = function(level, msg) {
    fail_on_missing_package('crayon')
    color <- switch(
        attr(level, 'level'),
        'FATAL' = crayon::combine_styles(crayon::bold, crayon::make_style('red1')),
        'ERROR' = crayon::make_style('red4'),
        'WARN'  = crayon::make_style('darkorange'),
        'INFO'  = crayon::reset,
        'DEBUG' = crayon::reset,
        'TRACE' = crayon::reset,
        stop('Unknown log level')
    )
    paste0(color(msg), crayon::reset(''))
}

#' nodoc
#' @export
InitLogger = function(logPath = "", threshold = INFO) {

    if (IsLoggerInited() && logPath == "" && threshold == INFO) {
        return(invisible())
    }

    if (logPath != "" && IsLoggerPathInited()) {
        warning("Cannot reinitialize the log path that already exsits:", .rutilLoggerPath)
        return(invisible())
    }

    # TODO config the log output file, layout, level
    if (!requireNamespace("logger", quietly = TRUE)) {
        stop("Missing logger pacakge")
    }
    InitLoggerForPython(logPath, threshold)

    logFormatter = "{RUtil:::ColorizedByLogLevel(levelr, paste0(
        {format(time, \"%Y-%m-%d %H:%M:%OS3\")},'|',
        {toTitleCase(tolower(level))},'|',
        {namespace},'|',
        {msg}))}"

    layout = layout_glue_generator(format = logFormatter)
    log_threshold(threshold)
    log_layout(layout)
    log_appender(appender_console)

    if (logPath != "") {
        log_threshold(TRACE, index = 2)
        log_layout(layout, index = 2)
        log_threshold(index = 2)
        log_appender(appender_file(logPath), index = 2)
        .rutilLoggerPath <<- logPath
        options(error = function() {
            ErrorLog("ExceptionHandler", "{geterrmessage()}")
            PrintStackTrace()
            if (!is.null(GetThisScriptPath())) {
                q(status = 1)
            }
        })
        options(warn = -1)
        log_warnings()
    }

    .rutilLoggerInitialized <<- TRUE

    logPathInfo = ifelse(logPath != "", glue("Log Path: {logPath}"), "")
    InfoLog("InitLogger", "Pid: {Sys.getpid()} {logPathInfo}")
}


#' nodoc
#' @export
InitLoggerForPython = function(logPath = "", threshold = INFO) {
    logLevel = switch(attr(threshold, "level"),
        "TRACE" = 0,
        "DEBUG" = 10,
        "INFO" = 20,
        "WARN" = 30,
        "SUCCESS" = 30,
        "ERROR" = 40,
        "FATAL" = 50)

    pyLoggerScript = system.file("Python", "Logger.py", package = "RUtil")
    InitPython(forceOverride = FALSE)
    source_python(pyLoggerScript)
    InitLogging(logPath, as.integer(logLevel))
}


#' nodoc
#' @export
TraceLog = function(loggerName, ...) {
    InitLogger()
    log_level(TRACE, ..., namespace = loggerName, .topenv = parent.frame(), .null = "")
}

#' nodoc
#' @export
DebugLog = function(loggerName, ...) {
    InitLogger()
    log_level(DEBUG, ..., namespace = loggerName, .topenv = parent.frame(), .null = "")
}

#' nodoc
#' @export
InfoLog = function(loggerName, ...) {
    InitLogger()
    log_level(INFO, ..., namespace = loggerName, .topenv = parent.frame(), .null = "")
}

#' nodoc
#' @export
WarningLog = function(loggerName, ...) {
    InitLogger()
    log_level(WARN, ..., namespace = loggerName, .topenv = parent.frame(), .null = "")
}

#' nodoc
#' @export
ErrorLog = function(loggerName, ...) {
    InitLogger()
    log_level(ERROR, ..., namespace = loggerName, .topenv = parent.frame(), .null = "")
}

#' nodoc
#' @export
FatalLog = function(loggerName, ..., stackTraceNSkip = 1) {
    InitLogger()
    log_level(FATAL, ..., namespace = loggerName, .topenv = parent.frame(), .null = "")
    PrintStackTrace(stackTraceNSkip, printStackVariable = FALSE)
    stop()
}

#' nodoc
#' @export
ProgressPrint = function(percent, ...) {
    InitLogger()
    if (log_threshold() <= logger::INFO) {
        cat("\r", format(percent * 100, digits = 4), "% ", ..., strrep(" ", 100))
        if (percent == 1) cat("\n")
    }
}