#' nodoc
#' @export
StrToTime = function(v, tz = GetConfig(DefaultTimezone)) {
    if (is.null(v)) return(NULL)
    return(as.POSIXct(v, tz, format = "%Y-%m-%d %H:%M:%OS"))
}

#' nodoc
#' @export
TimeToStr = function(v, nsmall = 9) {
    if (is.null(v)) return(NULL)
    return(paste0(format(v, format = "%Y-%m-%d %H:%M:%S"),
                  gsub("^-?[0-9]+", "", format(as.numeric(v), nsmall = nsmall))))
}

#' nodoc
#' @export
StrToDate = function(v, tz = GetConfig(DefaultTimezone)) {
    if (is.null(v)) return(NULL)
    return(as.POSIXct(v, tz, format = "%Y-%m-%d"))
}

#' nodoc
#' @export
PeriodToStr = function(begin, end, nsmall = 0) {
    b = TimeToStr(begin, nsmall)
    e = TimeToStr(end, nsmall)
    return(glue("[{b}, {e}]"))
}



#' nodoc
#' @export
DateToStr = function(v) {
    if (is.null(v)) return(NULL)
    return(format(v, format = "%Y-%m-%d"))
}

#' nodoc
#' @export
MonthToStr = function(v) {
    if (is.null(v)) return(NULL)
    return(format(v, format = "%Y-%m"))
}

#' nodoc
#' @export
YearToStr = function(v) {
    if (is.null(v)) return(NULL)
    return(format(v, format = "%Y"))
}



#' nodoc
#' @export
DoubleToTime = function(v, tz = GetConfig(DefaultTimezone)) {
    if (is.null(v)) return(NULL)
    return(as.POSIXct(v, tz, origin = ISOdate(1970, 1, 1, 0)))
}

#' nodoc
#' @export
MakeTime = function(v, tz = GetConfig(DefaultTimezone)) {
    if (is(v, "POSIXct")) {
        setattr(v, "tzone", tz)
        return(v)

    } else if (is(v, "POSIXlt")) {
        return(as.POSIXct(v))

    } else if (is(v, "numeric")) {
        return(DoubleToTime(v, tz))

    } else if (is(v, "Date")) {
        return(as.POSIXct(v))

    } else if (is(v, "character")) {
        if (grepl(":", first(v))) {
            return(StrToTime(v, tz))
        } else {
            return(StrToDate(v, tz))
        }

    } else {
        stop(paste0("Unknown v value: ", v))
    }
}

#' nodoc
#' @export
MakeTimeFast = function(v, v2 = 0, tz = GetConfig(DefaultTimezone)) {
    ParseHMS = function(v) {
        if (v < 0) {
            FatalLog("MakeTimeFast", "Invalid value: {v}")
        } else if (v <= 23) {
            return(hours(v))
        } else if (v <= 2359) {
            m = v %% 100
            h = (v - m) / 100
            return(hours(h) + minutes(m))
        } else if (v <= 235959) {
            s = v %% 100; v = (v - s) / 100
            m = v %% 100; v = (v - m) / 100
            h = v
            return(hours(h) + minutes(m) + seconds(s))
        } else {
            FatalLog("MakeTimeFast", "Invalid value: {v}")
        }
    }
    if (v < 235959) {
        return(ParseHMS(v) + GetToday(tz))
    } else {
        return(ParseHMS(v2) + ymd(v, tz = tz))
    }
}

#' nodoc
#' @export
TimezoneToTimediff = function(tz) {
    AssertNotNull(tz)
    return(as.POSIXlt(0, tz = tz, origin = "1970-01-01")$gmtoff)
}

#' nodoc
#' @export
GetNow = function(tz = GetConfig(DefaultTimezone)) {
    t = Sys.time()
    setattr(t, "tzone", tz)
    return(t)
}

#' nodoc
#' @export
GetToday = function(tz = GetConfig(DefaultTimezone)) {
    return(as.POSIXct(DateToStr(GetNow()), tz = tz))
}

#' nodoc
#' @param weekday 1: Monday, 7: Sunday
#' @export
GetPrevWeekday = function(weekday, from = GetToday()) {
    todayWday = wday(from, week_start = 1)
    daysToLastWday = ifelse(todayWday >= weekday, todayWday - weekday, 7 - weekday + todayWday)
    return(from - days(daysToLastWday))
}

#' nodoc
#' @export
IsDateFormatString = function(str) {
    return(grepl("^[12][0-9]{3}$", str) |
        grepl("^[12][0-9]{3}-(0[1-9]|1[012])$", str) |
        grepl("^[12][0-9]{3}-((0[1-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01]))$", str))
}

#' nodoc
#' @export
GetStrDateFormat = function(str) {
    if (length(str) == 0) return(NULL)

    if (grepl("^[12][0-9]{3}$", str)) {
        return("%Y")
    } else if (grepl("^[12][0-9]{3}-(0[1-9]|1[012])$", str)) {
        return("%Y-%m")
    } else if (grepl("^[12][0-9]{3}-((0[1-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01]))$", str)) {
        return("%Y-%m-%d")
    } else {
        WarningLog("GetStrDateFormat", "Unknown date format: {str}")
        NotImplemented()
    }
}

#' nodoc
#' @export
StrToDateByFormat = function(t, tz = GetConfig(DefaultTimezone)) {
    if (is.null(t)) return(NULL)

    format = GetStrDateFormat(first(t))
    if (format == "%Y") {
        ret = as.POSIXct(paste0(t, "-01-01"), tz)
    } else if (format == "%Y-%m") {
        ret = as.POSIXct(paste0(t, "-01"), tz)
    } else if (format == "%Y-%m-%d") {
        ret = StrToDate(t, tz)
    } else {
        NotImplemented()
    }
    return(ret)
}

#' nodoc
#' @export
DateToStrByFormat = function(t, format) {
    if (is.null(t) || is.null(format)) return(NULL)

    if (format %in% c("%Y", "%Y-%m", "%Y-%m-%d"))
        return(format(t, format = format))

    NotImplemented()
}