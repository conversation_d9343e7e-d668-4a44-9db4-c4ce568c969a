suppressMessages(library(Demeter))
# options(nwarnings = 2)
# options(warn = 1, showWarnCalls = TRUE)
source("/home/<USER>/VMHome/projects/Aegis/RUtil/R/Logger.r")

componentName = "TestWarnings"
logPath = GenLogPath("~/Log", componentName)
InitLogger(logPath)
InfoLog(componentName, "hello")
warning("test warnging")
InfoLog(componentName, "world")
for (i in 1:100) warning("Test warning ", i)
# InfoLog(componentName, "hello hello")
# InfoLog(componentName, "============")
# data = fread("~/test.csv", colClasses = list(integer = c("qty")))
# warnings()