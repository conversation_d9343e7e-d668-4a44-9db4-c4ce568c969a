suppressMessages(library(Fortuna))
suppressMessages(library(Venus))
suppressMessages(library(RUtil))
SetPreferredConcurrency()

InitRUtil()
InitDemeter()
InitCeres()

#' nodoc
#' @export
GetReportModuleLayout <- function(server, strategy) {
    layout <- list(
        modules = c("Performance", "Margin", "CancelCount", "OrderAction"),
        heights = c(3, 1, 1, 1, 1),
        ncol = 1
    )
}


SourceDir("/home/<USER>/VMHome/projects/Aegis/Fortuna/R")
# SourceDir("/home/<USER>/VMHome/projects/Aegis/Venus/R")
# ds <- GetCSVDataSource("~/RawData/MarketData/Apollo.devnet")
ds <- GetFSTDataSource("~/Data/MarketData")
ls <- GetFSTLogSource("~/Data/TradingLog")
InitTradingSession(ds)

reportPath <- "~/VMHome/report/"
begin = MakeTime("2025-04-01")
end = MakeTime("2025-04-30")
# server = c("dltra", "shgtra", "shftra")
server = c("shftra")

args <- NewReportArgs(
    dataSource = ds,
    logSource = ls,
    begin = begin,
    end = end,
    server = server,
    reportPath = reportPath,
    GetModuleLayout = GetReportModuleLayout,
    displayCfg = list(width = 1920, height = 1080, scale = 1),
    StrategyFilterForReport = function(server, strategy) {
        c("HFArb_ag68", "HFArb_au6a", "HFArb_pb56", "HFArb_ru59", "HFArb_zn567")
    }
)

ReportPerf(args)
