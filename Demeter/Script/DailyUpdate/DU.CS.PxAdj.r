suppressMessages(library(Demeter))
options(warn = 1, showWarnCalls = TRUE)

componentName = "DU.CS.PxAdj"
logPath = GenLogPath("~/Log", componentName)
InitLogger(logPath)

SetPreferredConcurrency()
InitRUtil()
InitDemeter()

cmdArgs = ProcessCmdArgs(NULL, list(targetDate = DateToStr(GetToday())))
targetDate = cmdArgs$targetDate

src = GetAKDataSource()
dest = GetFSTDataSource("~/Data/MarketData")
SetStateVar(componentName, "Doing", targetDate)

e = try_capture_stack({
    CopyData("ChinaStock", "Misc/PxAdj", src, dest)
}, environment())
if (OnException(e, "ChinaStock PxAdj failed")) {
    SetStateVar(componentName, "Failed", targetDate)
    FatalLog(componentName, "Failed to update ChinaStock PxAdj")
}

info = dest$ListData("ChinaStock", "Misc/PxAdj")
lastUpdateDate = max(info$end)
if (AddTradingDays(lastUpdateDate, 5, dest, "ChinaStock") < targetDate) {
    SetStateVar(componentName, "Failed", targetDate)
    FatalLog(componentName, "ChinaStock PxAdj has not updated for 5 trading days")
}

SetStateVar(componentName, "Done", targetDate)
InfoLog(componentName, "ChinaStock PxAdj done")