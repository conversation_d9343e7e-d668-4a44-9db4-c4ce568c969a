suppressMessages(library(Demeter))
options(warn = 1, showWarnCalls = TRUE)


logRoot = "~/Log"
InitLogger(GenLogPath(logRoot, "FixSettlementData"))

SetPreferredConcurrency()
InitDemeter()

marketType = "ChinaFuture"
# init
DefaultTimezone = Demeter::GetTimezone(marketType)

cmdArgs = ProcessCmdArgs("beginDate", list(crawl = FALSE))
if (is.null(cmdArgs)) quit()


beginDate = MakeTime(cmdArgs$beginDate)
beginTime = GetBeginOfTradingDayNoDS(beginDate, GetTradingDayCuttingHour(marketType), GetTradeOnWeekends(marketType))

rawDataRoot = "~/RawData/MarketData"
fstDataPath = "~/Data/MarketData"

exchRawDataPath = file.path(rawDataRoot, "ExchRaw/ChinaFuture/Settlement")
fstData = GetFSTDataSource(fstDataPath)

if (cmdArgs$crawl) {
    CrawlSettlementData(exchRawDataPath)
}

for (miscType in c("Misc/Margin", "Misc/Commission")) {
    RemoveMiscDataAfter(fstData, marketType, miscType, beginTime, needConfirm = FALSE)
    CopySettlementData(exchRawDataPath, miscType, fstData)
}
