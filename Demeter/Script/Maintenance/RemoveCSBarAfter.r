suppressMessages(library(Demeter))
options(warn = 1, showWarnCalls = TRUE)

componentName = "RemoveCSBarAfter"
InitLogger(GenLogPath("~/Log", componentName))
InfoLog(componentName, "Remove bar start")

SetPreferredConcurrency()
InitDemeter()

# ChinaETF also support.
args = ProcessCmdArgs(c("begin"), list(
    barSize = "60",
    marketType = "ChinaStock"
))
marketType = args$marketType
beginTime = MakeTime(args$begin)
barSize = args$barSize

root = "~/Data/MarketData"
ds = GetFSTDataSource(root)

year = year(beginTime)
yearBegin = MakeTime(glue("{year}-01-01"))
yearEnd = MakeTime(glue("{year + 1}-01-01"))
info = ds$ListData(marketType, glue("Bar/{barSize}"))
info = info[end > beginTime]
info = info[begin < yearEnd]

cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)
readBegin = GetBeginOfTradingDayNoDS(yearBegin, cuttingHour, tradeOnWeekends)
readEnd = GetBeginOfTradingDayNoDS(beginTime, cuttingHour, tradeOnWeekends)

path = file.path(root, marketType, glue("Bar/{barSize}/{year}"))
InfoLog(componentName, "Backup the folder: {path} before continue!!!")
Confirm(glue("Remove Bar/{barSize} data after {readEnd} for {marketType}, have backed up and continue?"))

for(sym in info$symbol) {
    InfoLog(componentName, "Removing: {sym}")
    data = ds$ReadData(marketType, glue("Bar/{barSize}"), sym, readBegin, readEnd)
    file = file.path(path, glue("{sym}.fst"))
    if (file.exists(file)) unlink(file)
    if (IsEmpty(data)) {
        InfoLog(componentName, "No data for {sym}, skipped")
        next
    }
    ds$WriteData(data)
    InfoLog(componentName, "Removed: {sym}")
}

InfoLog(componentName, "Remove bar done for {marketType}")
