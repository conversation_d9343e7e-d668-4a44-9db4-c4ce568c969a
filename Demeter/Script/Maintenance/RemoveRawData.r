suppressMessages(library(Demeter))
suppressMessages(library(Hades))
options(warn = 1, showWarnCalls = TRUE)

componentName = "RemoveRawData"
InitLogger(GenLogPath("~/Log", componentName))
InfoLog(componentName, "Remove data start")

SetPreferredConcurrency()
InitDemeter()
InitHades()

# init
defaultArgs = list(rtmdKeepNTradingDays = 10, needConfirm = TRUE)
args = ProcessCmdArgs(c("keepNTradingDays"), defaultArgs)
keepNTradingDays = as.integer(args$keepNTradingDays)
rtmdKeepNTradingDays = as.integer(args$rtmdKeepNTradingDays)
needConfirm = as.logical(args$needConfirm)
removeResult = TRUE

# remove market data
InfoLog(componentName, "Start removing data, keep trading days: {keepNTradingDays}")
dataPath = system.file("Data", package = "Demeter")
mdshFiles = list.files(dataPath, pattern = "^MarketDataSourceHistory_.*\\.csv$", full.names = TRUE)
allMDSH = rbindlist(lapply(mdshFiles, \(file) GetMDSH(file)))
allMDSH = allMDSH[updateMethod == "ArchiveCSVData" & !grepl("^Misc", dataType) & !grepl("Crypto", marketType)]
ds = GetFSTDataSource("~/Data/MarketData")

for (i in seq_len(nrow(allMDSH))) {
    item = MDSHParseItem(allMDSH[i])
    marketType = item$srcMarketType
    dataType = item$srcDataType
    date = AddTradingDays(GetToday(), -keepNTradingDays, ds, marketType)
    InfoLog(componentName, "Removing market data before: {date}, mkt/dt: {marketType}/{dataType}")

    for (j in seq_along(item$srcdsRoot)) {
        removeResult = RemoveRawDataBefore(
            item$srcdsRoot[j],
            marketType, dataType,
            date, item$dsRoot[j],
            needConfirm = needConfirm
        ) && removeResult
    }
}

# remove trading log
logPath = "~/RawData/TradingLog"
logArchivePath = "~/Archive/TradingLog"
date = AddTradingDays(GetToday(), -keepNTradingDays, ds, "ChinaFuture")
InfoLog(componentName, "Removing trading log before: {date}")
server = list.dirs(logPath, full.names = FALSE, recursive = FALSE)
for (svr in server) {
    removeResult = RemoveRawDataBefore(
        logPath, svr, "",
        date, logArchivePath,
        needConfirm = needConfirm
    ) && removeResult
}

# remove realtime market data
realtimeDataPath = "~/RawData/MarketData/Apollo.devnet"
rtds = GetCSVDataSource(realtimeDataPath)
for (marketType in rtds$ListData()) {
    dataTypes = rtds$ListData(marketType)
    dataTypes = dataTypes[!grepl("^Misc", dataTypes)]
    rtmdRemoveBeforeDate = AddTradingDays(GetToday(), -rtmdKeepNTradingDays, ds, marketType)
    InfoLog(componentName, "Removing realtime market data before: {rtmdRemoveBeforeDate}, mkt: {marketType}")

    for (dataType in dataTypes) {
        removeResult = RemoveRawDataBefore(
            realtimeDataPath,
            marketType,
            dataType,
            rtmdRemoveBeforeDate,
            compareArchiveData = FALSE,
            needConfirm = needConfirm
        ) && removeResult
    }
}

# finish
warnings()
InfoLog(componentName, "Remove data done")
WechatToDataWatcher(resultIsSuccess = removeResult, componentName, glue("Remove raw data, keep trading days: {keepNTradingDays}"))
