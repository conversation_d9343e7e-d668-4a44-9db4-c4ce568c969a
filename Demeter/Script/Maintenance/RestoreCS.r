suppressMessages(library(Demeter))
options(warn = 1, showWarnCalls = TRUE)


componentName = "RestoreCS"
logPath = GenLogPath("~/Log", componentName)
InitLogger(logPath)

SetPreferredConcurrency()
InitDemeter()

InfoLog(componentName, "Restore cs data start")

# init
DefaultTimezone = Demeter::GetTimezone("ChinaStock")

args = ProcessCmdArgs(c(), list(begin = "2020-01-01", CopyDataFlag = "FALSE"))
if (is.null(args)) quit()

begin = args$begin
if (!is.null(begin)) begin = MakeTime(begin)
CopyDataFlag = args$CopyDataFlag

rawDataPath = "~/RawData/MarketData"
fstDataPath = "~/Data/MarketData"
archivePath = "~/Archive/MarketData"

solidDataPath = file.path(rawDataPath, "Apollo.lmccsz")
solidDataArchivePath = file.path(archivePath, "Apollo.lmccsz")

standardCSVPath = file.path(rawDataPath, "Standard")
tempCSVPath = file.path(rawDataPath, "TempCSV")
standardArchivePath = file.path(archivePath, "Standard")


standardCSV = GetCSVDataSource(standardCSVPath)
tempCSV = GetCSVDataSource(tempCSVPath)
fstData = GetFSTDataSource(fstDataPath)

marketTypes = c("ChinaCvtBond", "ChinaPlgBond", "ChinaStock", "ChinaETF", "ChinaFund", "ChinaBond", "ChinaGovBond")
marketTypesNeedBar = c("ChinaETF", "ChinaStock", "ChinaCvtBond")

dataTypes = c("OESOrderbook10", "OESOrderFill", "OESOrderQueue", "OESOrderAction")


if (!is.null(begin)) {
    cuttingHour = GetTradingDayCuttingHour("ChinaStock")
    cuttingMethod = GetTradingDayCuttingMethod("ChinaStock")
    tradeOnWeekends = GetTradeOnWeekends("ChinaStock")
    begin = GetTradingDayNoDS(begin, cuttingHour, cuttingMethod, tradeOnWeekends)
}


## restore solid data
#for (marketType in marketTypes) {
    #for (dataType in dataTypes) {
        #RestoreFromArchive(solidDataArchivePath, solidDataPath, marketType, paste0("Solid", dataType), "xz", begin)
    #}
#}
#RestoreFromArchive(solidDataArchivePath, solidDataPath, "ChinaStock", "SolidOESIndexData", "xz", begin)


## restore standard data
#for (marketType in marketTypes) {
    #for (dataType in dataTypes) {
        #RestoreFromArchive(standardArchivePath, standardCSVPath, marketType, dataType, "xz", begin)
    #}
#}
#RestoreFromArchive(standardArchivePath, standardCSVPath, "ChinaStock", "OESIndexData", "xz", begin)


# restore Bar and Misc data
for (marketType in marketTypesNeedBar) {
    RestoreFromArchive(standardArchivePath, tempCSVPath, marketType, "Bar/60", "xz", begin)
}

for (marketType in marketTypes) {
    RestoreFromArchive(standardArchivePath, tempCSVPath, marketType, "Misc", "xz", begin)
}

RestoreFromArchive(standardArchivePath, tempCSVPath, "ChinaStock", "Fundamental", "xz", begin)


if (CopyDataFlag) {
    # copy standard data to fst
    for (marketType in marketTypes) {
        for (dataType in dataTypes) {
            CopyData(marketType, dataType, standardCSV, fstData)
        }
    }
    #CopyData("ChinaStock", "OESIndexData", standardCSV, fstData)


    # copy bar from tempCSV to fst
    for (marketType in marketTypesNeedBar) {
        CopyData(marketType, "Bar/60", tempCSV, fstData)
    }


    # copy Misc data from tempCSV to fst
    for (marketType in marketTypes) {
        CopyData(marketType, "Misc/TradingDay", tempCSV, fstData)
    }

    CopyData("ChinaStock", "Misc/PxAdj", tempCSV, fstData)
    CopyData("ChinaStock", "Misc/PxLimit", tempCSV, fstData)
    CopyData("ChinaETF", "Misc/Constituent", tempCSV, fstData)


    fundInfo = tempCSV$ListData("ChinaStock", "Fundamental")
    for (symbol in fundInfo$symbol) {
        dataType = paste0("Fundamental/", symbol)
        CopyData("ChinaStock", dataType, tempCSV, fstData)
    }

}

warnings()
InfoLog(componentName, "Restore cs data done")