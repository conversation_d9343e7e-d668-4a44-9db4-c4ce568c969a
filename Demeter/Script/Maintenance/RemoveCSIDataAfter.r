suppressMessages(library(Demeter))
options(warn = 1, showWarnCalls = TRUE)


SetPreferredConcurrency()
InitDemeter()
componentName = "RemoveCSIDataAfter"
InitLogger(GenLogPath("~/Log", componentName))

args = ProcessCmdArgs(NULL, list(date = GetToday(),
                                root = "~/Data/MarketData",
                                removeFST = TRUE,
                                rawDataRoot = "~/RawData/MarketData/",
                                removeCSV = FALSE))

date = MakeTime(args$date)
root = args$root
removeFST = as.logical(args$removeFST)
rawDataRoot = args$rawDataRoot
removeCSV = as.logical(args$removeCSV)
recycleBinRoot = glue("~/RawData/DailyUpdateLog/{GetToday()}/IndexConstituent/")


RemoveCSIFSTData = function(root, date, recycleBinRoot) {
    mkt = "ChinaStock"
    ds = GetFSTDataSource(root)
    data = ds$ReadData(mkt, "IndexConstituent")
    dataLeft = data[timestamp < date]
    datesToDel = MakeTime(setdiff(unique(data$timestamp), unique(dataLeft$timestamp)))

    if (length(datesToDel) == 0) {
        InfoLog(componentName, "No data to delete")
        return()
    }

    if (!Confirm(glue("Remove CSI data on {toString(datesToDel)}?"))) {
        InfoLog(componentName, "No move CSI data on {toString(datesToDel)}")
        return()
    }

    if (!dir.exists(recycleBinRoot)) {
        dir.create(recycleBinRoot, recursive = TRUE)
    }
    ys = unique(year(datesToDel))
    for (y in ys) {
        file = glue("{root}/ChinaStock/IndexConstituent/{y}.fst")
        res = file.copy(file, glue("{recycleBinRoot}/{basename(file)}.bak"))
        if (res) {
            res = file.remove(file)
        } else {
            ErrorLog(componentName, "File cannot be copyed: {file} => {recycleBinRoot}")
            return()
        }

        if (!res) {
            ErrorLog(componentName, "File cannot be removed: {file}")
            return()
        }
    }

    cuttingHour = GetTradingDayCuttingHour(mkt)
    cuttingMethod = GetTradingDayCuttingMethod(mkt)
    tradeOnWeekends = GetTradeOnWeekends(mkt)
    firstDayOfYear = GetBeginOfTradingYearNoDS(datesToDel, cuttingHour, cuttingMethod, tradeOnWeekends)
    dataLeft = dataLeft[dataLeft$timestamp >= firstDayOfYear]
    ds$WriteData(dataLeft)
    InfoLog(componentName, "CSI data on {datesToDel} moved: {file} => {recycleBinRoot}")
}


RemoveCSICSVData = function(rawDataRoot, date, recycleBinRoot) {
    if (date + days(25) < GetToday()) {
        FatalLog(componentName, "RawData 25 days before cannot be removed!")
        return()
    }

    folders = list.dirs(glue("{rawDataRoot}/ExchRaw/ChinaStock/IndexConstituent"), recursive = FALSE)
    foldersToDel = folders[as.Date(basename(folders)) > date]

    if (length(foldersToDel) > 1) {
        ErrorLog(componentName, "More than one folder to delete: {toString(basename(foldersToDel))}")
        return()
    }

    if (length(foldersToDel) == 0) {
        InfoLog(componentName, "No raw data to delete")
        return()
    }

    if (!Confirm(glue("Remove CSI raw data on {basename(foldersToDel)}?"))) {
        InfoLog(componentName, "No move CSI raw data on {basename(foldersToDel)}")
    }

    if (!dir.exists(recycleBinRoot)) {
        dir.create(recycleBinRoot, recursive = TRUE)
    }
    res = file.rename(foldersToDel, glue("{recycleBinRoot}/{basename(foldersToDel)}.bak"))
    if (!res) {
        ErrorLog(componentName, "Folder cannot be moved: {foldersToDel} => {recycleBinRoot}")
        return()
    }
    InfoLog(componentName, "CSI raw data on {basename(foldersToDel)} moved: {foldersToDel} => {recycleBinRoot}")
}

if (removeFST) {
    RemoveCSIFSTData(root, date, recycleBinRoot)
}

if (removeCSV) {
    RemoveCSICSVData(rawDataRoot, date, recycleBinRoot)
}
