FilterDataByTime = function(data, filterBegin, filterEnd) {

    if (is.null(data)) return(NULL)
    b = first(data[[1]])
    e = last(data[[1]])

    if (is.null(filterBegin)) filterBegin = b
    if (is.null(filterEnd)) filterEnd = e

    # all data within [filterBegin, filterEnd]
    if (b >= filterBegin && e <= filterEnd) return(data)

    # all data outside [filterBegin,filterEnd]
    if (b > filterEnd || e < filterBegin) return(NULL)

    return(data[data[[1]] >= filterBegin & data[[1]] <= filterEnd])
}

#' nodoc
#' @export
FilterDataByColumn = function(data, cols, nHeaderCols) {
    if (is.null(data)) return(NULL)

    dataCols = colnames(data)
    headerCols = c()
    if (nHeaderCols > 0) {
        headerCols = dataCols[1:nHeaderCols]
        dataCols = dataCols[(nHeaderCols + 1):length(dataCols)]
    }

    if (is.null(cols))
        cols = sort(dataCols)
    else
        cols = cols[cols %in% dataCols]
    if (length(cols) == 0) return(NULL)

    cols = c(headerCols, cols)
    return(data[, ..cols])
}





#==================================================================================================

ReadAndMerge = function(ReadFunc, paths, begin, end) {
    dataList = list()
    for (path in paths) {

        data = ReadFunc(path)
        if (IsEmpty(data)) next
        data = FilterDataByTime(data, begin, end)
        if (IsEmpty(data)) next
        dataList[[length(dataList) + 1]] = data
    }
    if (length(dataList) == 0) return(NULL)

    return(MyRbindlist(dataList, fill = TRUE))
}

ReadDailySplitData = function(marketType, dir, file, begin, end, ReadFunc) {
    Assert(begin <= end)
    tz = attr(begin, "tzone")
    cuttingHour = GetTradingDayCuttingHour(marketType)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)

    b = GetTradingDayNoDS(begin, cuttingHour, cuttingMethod, tradeOnWeekends)
    e = GetTradingDayNoDS(end, cuttingHour, cuttingMethod, tradeOnWeekends)

    days = seq(b, e, by = as.numeric(days(1)))
    days = DoubleToTime(days, tz)
    days = DateToStr(days)
    paths = file.path(dir, days, file)
    data = ReadAndMerge(ReadFunc, paths, begin, end)
    return(data)
}

#' nodoc
#' @export
ReadMonthlySplitData = function(dir, file, begin, end, ReadFunc) {
    Assert(begin <= end)
    tz = attr(begin, "tzone")
    months = seq(begin, end, by = as.numeric(days(25)))
    months = c(begin - days(3), months, end + days(3))
    months = DoubleToTime(months, tz)
    months = MonthToStr(months)
    months = unique(months)
    paths = file.path(dir, months, file)
    data = ReadAndMerge(ReadFunc, paths, begin, end)
    return(data)
}

ReadYearlySplitData = function(dir, file, begin, end, ReadFunc) {
    Assert(begin <= end)
    tz = attr(begin, "tzone")
    years = seq(begin, end, by = as.numeric(days(360)))
    years = c(begin - days(3), years, end + days(3))
    years = DoubleToTime(years, tz)
    years = YearToStr(years)
    years = unique(years)
    if (is.null(file)) {
        paths = file.path(dir, paste0(years, ".fst"))
    } else {
        paths = file.path(dir, years, file)
    }
    data = ReadAndMerge(ReadFunc, paths, begin, end)
    return(data)
}

ReadNosplitData = function(path, file, begin, end, ReadFunc) {
    data = ReadFunc(file.path(path, file))
    if (!is.null(data)) data = FilterDataByTime(data, begin, end)
    return(data)
}


#==================================================================================================
SplitAndWrite = function(data, cuttingPoints, cuttingMethod, dir, folderNameFormat, file, WriteFunc) {
    for (i in 1:(length(cuttingPoints) - 1)) {
        begin = cuttingPoints[i]
        end = cuttingPoints[i + 1]
        part = data[begin <= data[[1]] & data[[1]] < end]
        if (IsEmpty(part)) next

        date = format(switch(cuttingMethod, begin, end), format = folderNameFormat)
        if (is.null(file)) {
            path = file.path(dir, paste0(date, ".fst"))
        } else {
            path = file.path(dir, date, file)
        }
        WriteFunc(part, path)
    }
}

WriteDailySplitData = function(data, cuttingHour, cuttingMethod, tradeOnWeekends, dir, file, WriteFunc) {
    cuttingPoints = GetTradingDaySeqNoDS(first(data[[1]]), last(data[[1]]), cuttingHour, tradeOnWeekends)

    SplitAndWrite(data, cuttingPoints, cuttingMethod, dir, "%Y-%m-%d", file, WriteFunc)
}

#' nodoc
#' @export
WriteMonthlySplitData = function(data, cuttingHour, cuttingMethod, tradeOnWeekends, dir, file, WriteFunc) {
    cuttingPoints = GetTradingMonthSeqNoDS(first(data[[1]]), last(data[[1]]), cuttingHour, cuttingMethod, tradeOnWeekends)

    SplitAndWrite(data, cuttingPoints, cuttingMethod, dir, "%Y-%m", file, WriteFunc)
}

WriteYearlySplitData = function(data, cuttingHour, cuttingMethod, tradeOnWeekends, dir, file, WriteFunc) {
    cuttingPoints = GetTradingYearSeqNoDS(first(data[[1]]), last(data[[1]]), cuttingHour, cuttingMethod, tradeOnWeekends)

    SplitAndWrite(data, cuttingPoints, cuttingMethod, dir, "%Y", file, WriteFunc)
}





#==================================================================================================

#' nodoc
#' @export
ListSplitData = function(dir, fileExt, DataBeginEndReadFunc) {
    files = list.files(dir, full.names = FALSE, recursive = TRUE, pattern = paste0(".*\\.", fileExt))
    symbols = gsub(glue("(?:.*/)?([^/]+)\\.{fileExt}"), "\\1", files)
    uSym = unique(symbols)
    firstSymbolIdx = match(uSym, symbols)
    lastSymbolIdx = length(symbols) + 1 - match(uSym, rev(symbols))

    vbegin = numeric(length(uSym))
    vend = numeric(length(uSym))
    tz = NULL

    progressShowPerc = 1
    for (i in seq_along(uSym)) {
        firstFiles = files[firstSymbolIdx[i]]
        lastFiles = files[lastSymbolIdx[i]]

        firstPeriod = DataBeginEndReadFunc(file.path(dir, firstFiles))
        if (lastFiles == firstFiles) {
            lastPeriod = firstPeriod
        } else {
            lastPeriod = DataBeginEndReadFunc(file.path(dir, lastFiles))
        }

        vbegin[i] = firstPeriod$begin
        vend[i] = lastPeriod$end
        if (is.null(tz)) tz = attr(firstPeriod$begin, "tzone")
        percent = i / length(uSym)
        if (percent * 100 >= progressShowPerc) {
            PrintListDataProgress(uSym[i], firstPeriod$begin, lastPeriod$end, percent)
            progressShowPerc = progressShowPerc + 1
        }
    }

    vbegin = DoubleToTime(vbegin, tz)
    vend = DoubleToTime(vend, tz)
    return(data.table(begin = vbegin, end = vend, symbol = uSym))
}

ListNosplitData = function(dir, fileExt, DataBeginEndReadFunc) {
    vbegin = vector()
    vend = vector()
    vsymbol = vector()

    files = list.files(dir, pattern = paste0(".*\\.", fileExt))
    tz = NULL
    for (file in files) {
        period = DataBeginEndReadFunc(file.path(dir, file))
        vbegin = c(vbegin, period$begin)
        vend = c(vend, period$end)
        vsymbol = c(vsymbol, gsub(paste0("\\.", fileExt), "", file))
        if (is.null(tz)) tz = attr(period$begin, "tzone")

        percent = (match(file, files) / length(files))
        PrintListDataProgress(last(vsymbol), DoubleToTime(last(vbegin), tz), DoubleToTime(last(vend), tz), percent)
    }
    if (!length(vsymbol)) return(NULL)
    return(data.table(begin = DoubleToTime(vbegin, tz), end = DoubleToTime(vend, tz), symbol = vsymbol))
}

ListDailySplitData = function(dir, fileExt, tz, cuttingHour, tradeOnWeekends) {
    dataList = list()
    folders = list.dirs(dir, full.names = FALSE, recursive = FALSE)
    for (folder in folders) {

        time = as.POSIXct(folder, format = "%Y-%m-%d", tz = tz)
        if (is.na(time)) next

        files = list.files(file.path(dir, folder), pattern = paste0(".*\\.", fileExt))
        if (length(files) == 0) next

        begin = GetBeginOfTradingDayNoDS(time, cuttingHour, tradeOnWeekends)
        end = GetBeginOfNextTradingDayNoDS(time, cuttingHour, tradeOnWeekends)
        symbols = gsub(paste0("\\.", fileExt), "", files)
        dataList[[length(dataList) + 1]] = data.table(begin = begin, end = end, symbol = symbols)

        percent = match(folder, folders) / length(folders)
        PrintListDataProgress(last(symbols), begin, end, percent)
    }

    ret = rbindlist(dataList)
    if (IsEmpty(ret)) NULL else ret
}

#' nodoc
#' @export
DataDetailToBrief = function(detail) {
    if (IsEmpty(detail)) return(detail)

    vsymbol = sort(unique(detail$symbol))
    vbegin = vector()
    vend = vector()
    for (s in vsymbol) {
        part = detail[symbol == s]
        vbegin = c(vbegin, base::min(part$begin))
        vend = c(vend, base::max(part$end))
    }
    ret = data.table(begin = vbegin, end = vend, symbol = vsymbol)
    tz = attr(detail$begin, "tzone")
    ret = ret[, begin := DoubleToTime(begin, tz)]
    ret = ret[, end := DoubleToTime(end, tz)]
    ret[]
    return(ret)
}
