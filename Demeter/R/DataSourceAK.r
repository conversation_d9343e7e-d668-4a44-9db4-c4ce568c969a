# ==================================================================================================
# utilities
# static method
AKTryCatch = function(expr) {
    retry = 0
    while(retry < 3) {
        e = try_capture_stack({
            ret = eval(substitute(expr), envir = parent.frame())
            break
        }, environment())

        if (is.error(e)) {
            WarningLog("AKTryCatch", glue("Failed to call ak api {deparse(substitute(expr))}: {e$message}. Retrying..."))
        }
        Sys.sleep(retry * 2 + 1)
        retry = retry + 1
        if (retry == 3) {
            ErrorLog("AKTryCatch", glue("Failed to call ak api {deparse(substitute(expr))}"))
            return(NULL)
        }
    }
    return(ret)
}

AKUpdateSymbolMaps = function(marketType, force) {
    if (!force && !is.null(symbolMaps[[marketType]])) {
        return()
    }

    if (marketType == "ChinaStock") {
        emCS = TryCatch(ak$stock_zh_a_spot_em())
        symbol = emCS[[2]]
        if (length(symbol) < 4000) {
            snCS = TryCatch(ak$stock_zh_a_spot())
            snSymbols = substring(snCS[[1]], 3)
            symbol = unique(c(emCS[[2]], snSymbols))
        }
        symbol = symbol[!(startsWith(symbol, "8") | startsWith(symbol, "4") | startsWith(symbol, "920"))]
        fullSymbol = AddExchPrefixToStockCode(symbol)
        map = data.table(symbol = symbol, fullSymbol = fullSymbol)
    } else if (marketType == "ChinaCvtBond") {
        thsCvt = TryCatch(ak$bond_zh_cov_info_ths())
        emCvt = TryCatch(ak$bond_zh_cov())
        symbol = unique(c(thsCvt[[1]], emCvt[[1]]))
        map = data.table(symbol = symbol)
    } else if (marketType == "ChinaETF") {
        emETF = TryCatch(ak$fund_etf_spot_em())
        em = data.table(emETF)
        em = em[!is.nan(em[[4]])] # Remove rows whoese IPOV realtime estimate is NaN, which means the ETF is Money Market Fund
        thsETF = TryCatch(ak$fund_etf_spot_ths())
        symbol = unique(c(em[[1]], thsETF[[2]]))
        map = data.table(symbol = symbol)
    } else if (marketType == "ChinaPlgBond") {
        map = data.table(symbol = c("131800", "131801", "131802", "131803", "131805", "131806", "131809", 
                        "131810", "131811", "204001", "204002", "204003", "204004", "204007", "204014",
                        "204028", "204091", "204182"))
    } else if (marketType == "ChinaIndex") {
        emCIsh = TryCatch(ak$stock_zh_index_spot_em("上证系列指数"))
        emCIsz = TryCatch(ak$stock_zh_index_spot_em("深证系列指数"))
        # emCIsina = TryCatch(ak$stock_zh_index_spot_sina())
        # sinaSymbol = substring(emCIsina[[1]], 3)
        symbol = sort(unique(c(emCIsh[[2]], emCIsz[[2]])))#, sinaSymbol)))
        symbol = ifelse(startsWith(symbol, "000"), paste0("sh", symbol), paste0("sz", symbol))
        map = data.table(symbol = symbol)
    } else {
        NotImplemented()
    }

    setorder(map, symbol)
    symbolMaps[[marketType]] <<- map
}

AKAddExchPrefixToStockCode = function(symbols) {
    ret = sapply(symbols, function(symbol) {
        if (startsWith(symbol, "6")) {
            return(paste0("sh", symbol))
        } else if (startsWith(symbol, "0") || startsWith(symbol, "3")) {
            return(paste0("sz", symbol))
        } else if (startsWith(symbol, "8") || startsWith(symbol, "4") || startsWith(symbol, "920")) {
            return(paste0("bj", symbol))
        } else {
            return(symbol)
        }
    })
    names(ret) = NULL
    return(ret)
}

AKGetFullSymbol = function(marketType, dataType, symbols) {
    UpdateSymbolMaps(marketType, FALSE)
    if (is.null(symbols)) {
        if (marketType == "ChinaStock") {
            return(symbolMaps[[marketType]]$fullSymbol)
        } else {
            NotImplemented()
        }
    }

    symbolMap = symbolMaps[[marketType]]
    fullSymbol = symbolMap[symbol %in% symbols]$fullSymbol
    return(fullSymbol)
}

GetDataTypeOfAKSupportMarketType = function(marketType) {
    map = list(
        ChinaStock = c("Misc/PxAdj"),
        ChinaCvtBond = NULL,
        ChinaETF = NULL
    )

    if (marketType %in% names(map)) {
        return(map[[marketType]])
    }
    FatalLog("GetDataTypeOfAKSupportMarketType", "Unsupported market type: {marketType}")
}

GetAKSupportMarketType = function() {
    return(c("ChinaStock", "ChinaCvtBond", "ChinaETF", "ChinaPlgBond", "ChinaIndex"))
}

AKInvalidateCache = function() {
    infoCache <<- list()
}

AKGetProducts = function(marketType) {
    if (marketType %in% GetAKSupportMarketType()) {
        UpdateSymbolMaps(marketType, TRUE)
        return(symbolMaps[[marketType]]$symbol)
    } else {
        FatalLog("AKGetProducts", "Unsupported market type: {marketType}")
    }
}

AKGetExchange = function(symbol) {
    if (any(startsWith(symbol, c("sh", "6", "5", "110", "111", "113", "118", "204")))) { # cs/ci, cs, etf, ccb, ccb, ccb, ccb, plg
        return("XSHG")
    } else if (any(startsWith(symbol, c("sz", "0", "3", "15", "12", "4", "115", "131")))) { # cs/ci, cs, cs, etf, ccb, ccb, ccb, plg
        return("XSHE")
    }
    ErrorLog("AKGetExchange", "Unknown exchange for symbol: {symbol}")
    return(NULL)
}

AKGenerateProductXmlContent = function(marketType) {
    if (marketType %in% GetAKSupportMarketType()) {
        root = newXMLNode("Products")
        tickSize = 0.001
        type = "CvtBond"
        if (marketType == "ChinaStock") {
            tickSize = 0.01
            type = "Stock"
        } else if (marketType == "ChinaPlgBond") {
            tickSize = 0.005
            type = "PlgBond"
        } else if (marketType == "ChinaETF") {
            type = "ETF"
        } else if (marketType == "ChinaIndex") {
            type = "Index"
            tickSize = 0.01
        }

        products = GetProducts(marketType)
        for (i in 1:length(products)) {
            ProgressPrint(i / length(products), glue("Generating product XML for {marketType}: {i}/{length(products)}"))
            attr = list(
                exchange = AKGetExchange(products[i]),
                symbol = products[i],
                type = type,
                tickSize = tickSize,
                contractSize = 1,
                duisuo = "false",
                kxpz = "true",
                reqCloseToday = "true"
            )
            if (marketType == "ChinaIndex") {
                attr$symbol = paste0("Idx", substring(products[i], 3))
                attr$duisuo = NULL
                attr$kxpz = NULL
                attr$reqCloseToday = NULL
            }
            newXMLNode("Product", attrs = attr, parent = root)
        }
        return(root)
    } else {
        NotImplemented()
    }
}

AKGetProductDataFromXmlContent = function(xmlContent) {
    products = getNodeSet(xmlContent, "//Product")
    productData = data.table(
        exchange = sapply(products, function(node) xmlGetAttr(node, "exchange")),
        symbol = sapply(products, function(node) xmlGetAttr(node, "symbol")),
        type = sapply(products, function(node) xmlGetAttr(node, "type")),
        tickSize = as.numeric(sapply(products, function(node) xmlGetAttr(node, "tickSize"))),
        contractSize = as.numeric(sapply(products, function(node) xmlGetAttr(node, "contractSize"))),
        duisuo = sapply(products, function(node) xmlGetAttr(node, "duisuo", FALSE)),
        kxpz = sapply(products, function(node) xmlGetAttr(node, "kxpz", FALSE)),
        reqCloseToday = sapply(products, function(node) xmlGetAttr(node, "reqCloseToday", FALSE))
    )
    return(productData)
}

# ==================================================================================================
# implementation
AKReadMiscPxAdj = function(marketType, symbols, begin, end) {
    if (marketType != "ChinaStock") {
        FatalLog("AKReadMiscPxAdj", "Unsupported market type: {marketType}")
    }

    tz = GetTimezone(marketType)
    begin = StrToDate(strftime(begin, tz = tz), tz)
    end = StrToDate(strftime(end, tz = tz), tz) + hours(17)

    adj = lapply(symbols, function(symbol) {
        i = which(symbols == symbol)
        ProgressPrint(i / length(symbols), glue("Reading misc/pxadj data for {substring(symbol, 3)}"))
        obj = TryCatch(ak$stock_zh_a_daily(symbol, adjust = "qfq-factor"))
        if (IsEmpty(obj)) {
            return(NULL)
        }
        values = ParsePyDfWithNaT(obj)
        if (IsEmpty(values)) {
            return(NULL)
        }

        ret = data.table(
            timestamp = values$date,
            symbol = substring(symbol, 3),
            adjustment = c(as.numeric(values$qfq_factor[-1]) / as.numeric(values$qfq_factor[-length(values$qfq_factor)]), 1.0)
        )

        ret = ret[order(timestamp)]
        ret = ret[timestamp >= begin & timestamp <= end]
        return(ret)
    })
    ret = rbindlist(adj)
    if (IsEmpty(ret)) {
        return(NULL)
    }
    setorderv(ret, c("timestamp", "symbol"))
    return(ret)
}


AKReadData = function(marketType, dataType, symbol, begin = GetConfig(DefaultBegin), end = GetConfig(DefaultEnd)) {
    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    dateBegin = DateToStr(GetTradingDayNoDS(begin, cuttingHour, cuttingMethod, tradeOnWeekends))
    dateEnd = DateToStr(GetTradingDayNoDS(end, cuttingHour, cuttingMethod, tradeOnWeekends))

    fullSymbol = GetFullSymbol(marketType, dataType, symbol)
    if (marketType == "ChinaStock") {
        if (dataType == "Misc/PxAdj") {
            data = ReadMiscPxAdj(marketType, fullSymbol, dateBegin, dateEnd)
        } else {
            NotImplemented()
        }
    } else {
        NotImplemented()
    }

    # data = FilterDataByTime(data, begin, end)
    if (is.null(data)) {
        return(NULL)
    }

    setattr(data, "marketType", marketType)
    setattr(data, "dataType", dataType)
    setattr(data, "symbol", symbol)

    return(data)
}

AKListData = function(marketType = NULL, dataType = NULL) {
    if(is.null(marketType)) {
        return(GetAKSupportMarketType())
    }

    if(is.null(dataType)) {
        return(GetDataTypeOfAKSupportMarketType(marketType))
    }

    cacheName = paste0(marketType, "/", dataType)
    if (!is.null(infoCache[[cacheName]])) {
        return(infoCache[[cacheName]])
    }

    if (marketType == "ChinaStock" && dataType == "Misc/PxAdj") {
        UpdateSymbolMaps(marketType, TRUE)
        symbol = symbolMaps[[marketType]]$symbol
        beginDay = "2000-01-01"
    } else {
        NotImplemented()
    }

    if (is.null(symbol) || length(symbol) == 0) {
        return(NULL)
    }
    tz = GetTimezone(marketType)
    info = data.table(begin = StrToDate(beginDay, tz), end = GetNow(tz), symbol = symbol)
    infoCache[[cacheName]] <<- info

    return(info)
}


AKSupportMultiSymbolOperation = function(marketType, dataType) {
    return(TRUE)
}

# ==================================================================================================
# facade
#' nodoc
#' @export
GetAKDataSource = function() {
    return(AKDataSourceGenerator$new())
}

#' nodoc
#' @export
AKDataSourceGenerator = setRefClass("AKDataSource",
    contains = "DataSource",
    fields = c("ak", "infoCache", "symbolMaps"),
    methods = list(
        initialize = function() {
            .self$ak = reticulate::import("akshare")
            .self$infoCache = list()
            .self$symbolMaps = list()
            pyUtilFile = system.file("Python", "PyUtil.py", package = "Demeter")
            source_python(pyUtilFile, envir = globalenv(), convert = F)
        },
        # ===== interface functions ======
        ReadData = AKReadData,
        WriteData = function(data) {
            FatalLog("AKDataSourceGenerator", "AK data source is readonly")
        },
        ListData = AKListData,
        SupportMultiSymbolOperation = AKSupportMultiSymbolOperation,
        InvalidateCache = AKInvalidateCache,
        GetProducts = AKGetProducts,
        GetExchange = AKGetExchange,
        GenerateProductXmlContent = AKGenerateProductXmlContent,
        GetProductDataFromXmlContent = AKGetProductDataFromXmlContent,
        ReadMiscPxAdj = AKReadMiscPxAdj,
        GetDesc = function() { return("<AKDataSource>") },

        # ===== other functions ==========
        GetFullSymbol = AKGetFullSymbol,
        UpdateSymbolMaps = AKUpdateSymbolMaps,
        AddExchPrefixToStockCode = AKAddExchPrefixToStockCode,
        TryCatch = AKTryCatch
    )
)