# ==================================================================================================
# utilities
GetDataColClass = function(marketType, dataType) {
    colClass = NULL
    if (startsWith(dataType, "Misc/")) {
        if (dataType == "Misc/Constituent") {
            colClass = list(character = c("symbol", "constituent"))
        } else if (dataType == "Misc/IndustryMap") {
            colClass = list(character = c("symbol", "first_industry_code", "second_industry_code", "third_industry_code"))
        } else if (!dataType %in% c(
            "Misc/Session", "Misc/Major", "Misc/OIMajor", "Misc/TradingDay",
            "Misc/ExchangeRebate", "Misc/OACommission"
        )) {
            colClass = list(character = "symbol")
        }
    } else if (startsWith(dataType, "Bar/")) {
        colClass = list(numeric = c("open", "high", "low", "close", "vol", "value"))
    } else if (marketType == "ChinaFuture" || marketType == "ChinaOption") {
        if (dataType == "Tick") {
            colClass = list(
                numeric = c("bidpx", "askpx", "lastpx", "totalvol", "totalvalue", "openinterest"),
                integer = c("bidvol", "askvol")
            )
        } else if (dataType == "Best") {
            colClass = list(
                numeric = c("bidpx", "askpx", "lastpx", "totalvol", "totalvalue", "openinterest", "open", "high", "low", "close", "avgPx"),
                integer = c("bidvol", "bidImplyVol", "askvol", "askImplyVol", "lastVol", "lastOI", "oiChg")
            )
        } else if (dataType == "Deep") {
            colClass = list(
                numeric = c(
                    "bidpx0", "bidpx1", "bidpx2", "bidpx3", "bidpx4",
                    "askpx0", "askpx1", "askpx2", "askpx3", "askpx4"
                ),
                integer = c(
                    "bidvol0", "bidvol1", "bidvol2", "bidvol3", "bidvol4",
                    "askvol0", "askvol1", "askvol2", "askvol3", "askvol4",
                    "bidImplyVol0", "bidImplyVol1", "bidImplyVol2", "bidImplyVol3", "bidImplyVol4",
                    "askImplyVol0", "askImplyVol1", "askImplyVol2", "askImplyVol3", "askImplyVol4"
                )
            )
        } else if (dataType == "MatchPriceQty") {
            colClass = list(
                numeric = c(
                    "price0", "price1", "price2", "price3", "price4"
                ),
                integer = c(
                    "bo0", "bo1", "bo2", "bo3", "bo4",
                    "bc0", "bc1", "bc2", "bc3", "bc4",
                    "so0", "so1", "so2", "so3", "so4",
                    "sc0", "sc1", "sc2", "sc3", "sc4"
                )
            )
        } else if (dataType == "TenEntrust") {
            colClass = list(
                numeric = c("bidpx", "askpx"),
                integer = c(
                    "bidOrderQty0", "bidOrderQty1", "bidOrderQty2", "bidOrderQty3", "bidOrderQty4",
                    "bidOrderQty5", "bidOrderQty6", "bidOrderQty7", "bidOrderQty8", "bidOrderQty9",
                    "askOrderQty0", "askOrderQty1", "askOrderQty2", "askOrderQty3", "askOrderQty4",
                    "askOrderQty5", "askOrderQty6", "askOrderQty7", "askOrderQty8", "askOrderQty9"
                )
            )
        } else if (dataType == "OrderStatistic") {
            colClass = list(
                numeric = c("vwabp", "vwaap"),
                integer = c("nBuyOrder", "nSellOrder")
            )
        } else if (dataType == "Orderbook5") {
            colClass = list(
                numeric = c(
                    "bidpx4", "bidpx3", "bidpx2", "bidpx1", "bidpx0",
                    "askpx0", "askpx1", "askpx2", "askpx3", "askpx4",
                    "lastpx", "totalvol", "totalvalue", "openinterest"
                ),
                integer = c(
                    "bidvol4", "bidvol3", "bidvol2", "bidvol1", "bidvol0",
                    "askvol0", "askvol1", "askvol2", "askvol3", "askvol4"
                )
            )
        } else if (dataType == "Orderbook10") {
            colClass = list(
                numeric = c(
                    "bidpx9", "bidpx8", "bidpx7", "bidpx6", "bidpx5",
                    "bidpx4", "bidpx3", "bidpx2", "bidpx1", "bidpx0",
                    "askpx0", "askpx1", "askpx2", "askpx3", "askpx4",
                    "askpx5", "askpx6", "askpx7", "askpx8", "askpx9"
                ),
                integer = c(
                    "bidvol9", "bidvol8", "bidvol7", "bidvol6", "bidvol5",
                    "bidvol4", "bidvol3", "bidvol2", "bidvol1", "bidvol0",
                    "askvol0", "askvol1", "askvol2", "askvol3", "askvol4",
                    "askvol5", "askvol6", "askvol7", "askvol8", "askvol9"
                )
            )
        }
    } else if (marketType %in% c(
        "ChinaStock", "ChinaBond", "ChinaCvtBond",
        "ChinaGovBond", "ChinaPlgBond", "ChinaETF", "ChinaFund"
    )) {
        if (dataType %in% c("OESOrderbook10", "SipOrderbook10")) {
            colClass = list(
                numeric = c(
                    "bidpx9", "bidpx8", "bidpx7", "bidpx6", "bidpx5",
                    "bidpx4", "bidpx3", "bidpx2", "bidpx1", "bidpx0",
                    "askpx0", "askpx1", "askpx2", "askpx3", "askpx4",
                    "askpx5", "askpx6", "askpx7", "askpx8", "askpx9",
                    "prevclose", "open", "high", "low", "close", "lastpx", "totalvol",
                    "totalvalue", "ntrades", "totalbidvol", "totalaskvol", "vwabp", "vwaap"
                ),
                integer = c(
                    "bidvol9", "bidvol8", "bidvol7", "bidvol6", "bidvol5",
                    "bidvol4", "bidvol3", "bidvol2", "bidvol1", "bidvol0",
                    "askvol0", "askvol1", "askvol2", "askvol3", "askvol4",
                    "askvol5", "askvol6", "askvol7", "askvol8", "askvol9"
                )
            )
            if (dataType == "OESOrderbook10") {
                colClass[["numeric"]] = c(colClass$numeric, "iopv", "openinterest")
            }
        } else if (dataType %in% c("OESOrderAction", "SipOrderAction")) {
            colClass = list(numeric = "price", integer = "qty")
        } else if (dataType %in% c("OESOrderFill", "SipOrderFill")) {
            colClass = list(numeric = c("price", "value"), integer = "qty")
        } else if (dataType == "OESOrderQueue") {
            colClass = list(numeric = c("bidpx", "askpx"), character = c("bidqty", "askqty"))
        } else if (dataType == "OESIndexData") {
            colClass = list(numeric = c("prevclose", "open", "high", "low", "close", "totalvol", "totalvalue"), integer = "nstocks")
        } else if (dataType == "RemdOptionOrderbook5") {
            colClass = list(
                numeric = c(
                    "bidpx0", "bidpx1", "bidpx2", "bidpx3", "bidpx4",
                    "askpx0", "askpx1", "askpx2", "askpx3", "askpx4",
                    "lastpx", "totalvalue", "open", "high", "low", "prevClose", "close",
                    "prevSettlePx", "settlePx", "prevOpenInterest", "openinterest",
                    "totalvol", "prevDelta", "delta", "auctionPx"
                ),
                integer = c(
                    "bidvol0", "bidvol1", "bidvol2", "bidvol3", "bidvol4",
                    "askvol0", "askvol1", "askvol2", "askvol3", "askvol4",
                    "auctionQty"
                )
            )
        } else if (dataType == "RemdOrderAction") {
            colClass = list(numeric = c("price"), integer = c("qty", "side"), integer64 = c("orderId"))
        } else if (dataType == "RemdOrderActionSH") {
            colClass = list(numeric = c("price"), integer = c("qty"), integer64 = c("orderId", "rawOrderId", "bizId"))
        } else if (dataType == "RemdOrderbook10") {
            colClass = list(
                numeric = c(
                    paste0("bidpx", 0:9), paste0("askpx", 0:9),
                    "lastpx", "totalvol", "totalvalue", "totalBidVol", "totalAskVol",
                    "prevClose", "open", "high", "low", "vwabp", "vwaap", "iopv"
                ),
                integer = c(
                    paste0("bidvol", 0:9), paste0("askvol", 0:9),
                    "nTrades"
                )
            )
        } else if (dataType == "RemdOrderFill") {
            colClass = list(numeric = c("price", "value"), integer = c("qty", "side"), integer64 = c("id", "bidId", "askId"))
        } else if (dataType == "RemdOrderQueue") {
            colClass = list(numeric = c("price"), integer = c("nOrders", paste0("qty", 0:199)))
        } else if (dataType == "RemdSpotTick") {
            colClass = list(
                numeric = c("price", "tradeMoney"),
                integer = c("qty")
            )
        } else if (dataType == "RemdIndexData") {
            colClass = list(
                numeric = c("prevClose", "open", "high", "low", "last", "totalvol", "totalvalue")
            )
        } else if (dataType == "IndexConstituent") {
            colClass = list(
                character = c("symbol", "constituent"),
                numeric = c("valueWeight", "closePx", "qty")
            )
        }
    }

    return(colClass)
}
