PrintListDataProgress = function(symbol, begin, end, percent) {
    ProgressPrint(percent, paste0("Getting ", symbol, ":\t ", begin, " => ", end))
}

#' nodoc
#' @export
PyIsNone = function(x) {
    return(length(x) == 0 || as.character(x) == "None")
}

#' nodoc
#' @export
ParsePyDf = function(df) {
    if (PyIsNone(df)) return(NULL)
    ret = py_to_r(df$reset_index())
    setDT(ret)
    return(ret)
}

MyRbindlist = function(l, ...) {
    resultNames = NULL
    maxLength = 0
    for (i in seq_along(l)) {
        ns = names(l[[i]])
        if (is.null(ns)) next
        if (length(ns) > maxLength) {
            maxLength = length(ns)
            resultNames = ns
        }
    }
    result = rbindlist(l, ...)
    if (!is.null(resultNames))
        setcolorder(result, resultNames)
    else
        WarningLog("MyRbindlist", "Input data each has different column, so colunms order is default")

    return(result)
}

#' nodoc
#' @export
ExtractImportData = function(server, type, date, updateProductXml = FALSE, hasTradingLog = TRUE, ext = "gz") {
    targetDir = glue("~/RawData/TradingLog/{server}/{date}")
    if (hasTradingLog & dir.exists(targetDir)) {
        WarningLog("ExtractImportData", "Target dir not empty: {targetDir}")
        return(FALSE)
    }

    tempDir = "~/RawData/"
    tarCmd = glue("tar -xvf ~/ServerDataImport/{server}/{server}.{type}.{date}.tar.{ext} -C {tempDir}")
    res = BashExec(tarCmd)
    if (!res) {
        WarningLog("ExtractImportData", "Failed to exec: {tarCmd}.")
        return(FALSE)
    }

    if (hasTradingLog) {
        targetDir = glue("~/RawData/TradingLog/{server}")
        CreateDir(targetDir)
        src = glue("{tempDir}/Log/Zeus.{server}/{date}")
        dst = glue("{targetDir}/{date}")
        res = file.rename(src, dst)
        if (!res) {
            WarningLog("ExtractImportData", "Failed to move {src} => {dst}")
            return(FALSE)
        }
    }

    if (updateProductXml) {
        res = BashExec(glue("mv {tempDir}/Products/* ~/Config/Products/"))
        if (!res) {
            WarningLog("ExtractImportData", "Failed to update product xml.")
        }
    }
    return(res)
}

#' nodoc
#' @export
SyncDataToCluster = function(server, marketType) {
    thisYear = YearToStr(GetToday())
    if (marketType == "ChinaFuture") {
        dirs = c("~/Config/Products",
                "~/Config",
                "~/Data/MarketData/ChinaFuture/Bar",
                "~/Data/MarketData/ChinaFuture/Misc",
                "~/Data/MarketData/ChinaFuture/Tick",
                "~/Data/MarketData/ChinaFuture/Orderbook5",
                "~/Data/MarketData/InfoCache"
                )
        patterns = c("", "Hades.xml", "", "", glue("{thisYear}*"), glue("{thisYear}*"), "ChinaFuture*")
    } else if (marketType == "ChinaStock") {
        dirs = c("~/Data/MarketData/ChinaStock/Bar",
                "~/Data/MarketData/ChinaStock/Misc",
                "~/Data/MarketData/ChinaStock/Tick",
                "~/Data/MarketData/ChinaStock/Orderbook5",
                "~/Data/MarketData/InfoCache"
                )
        patterns = c("", "", glue("{thisYear}*"), glue("{thisYear}*"), "ChinaStock*", "ChinaStock*")
    }
    dirs = path.expand(dirs)

    srcs = sapply(1:length(dirs), function(i) {
        echoCmd = glue("echo {dirs[i]}/./{patterns[i]}")
        res = system(echoCmd, intern = TRUE)
        return(res)
    })

    results = sapply(1:length(dirs), function(i) {
        mkdirCmd = glue("ssh {server} mkdir -p {dirs[i]}")
        InfoLog("SyncDataToCluster", "mkdirCmd: {mkdirCmd}")
        BashExec(mkdirCmd)

        syncCmd = glue("rsync -aiR {srcs[i]} {server}:{dirs[i]}")
        InfoLog("SyncDataToCluster", "syncCmd: {syncCmd}")
        return(BashExec(syncCmd))
    })
    return(!any(!results))
}

#' nodoc
#' @export
GetTradingDayOfYear = function(year, holidays, marketType) {
    tz = GetTimezone(marketType)
    cuttingMethod = Demeter::GetTradingDayCuttingMethod(marketType)
    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)

    allDates = seq.POSIXt(from = MakeTime(glue("{year}-01-01"), tz = tz),
                          to = MakeTime(glue("{year}-12-31"), tz = tz),
                          by = "day")

    dt = data.table(tradingday = allDates)
    dt = dt[!weekdays(tradingday) %in% c("Saturday", "Sunday")]
    dt = dt[!(tradingday %in% holidays)]

    if (cuttingMethod == 1) {
        dt[, timestamp := GetBeginOfTradingDayNoDS(tradingday, cuttingHour, tradeOnWeekends)]
    } else {
        # assume the last weekday of previous year is not public holiday.
        # if violated, add that holiday to the input csv to workaround
        prevDay = dt$tradingday[1] - days(1)
        while (weekdays(prevDay) %in% c("Saturday", "Sunday") || prevDay %in% holidays) {
            prevDay = prevDay - days(1)
        }
        dt[, timestamp := GetBeginOfNextTradingDayNoDS(c(prevDay, tradingday[-.N]), cuttingHour, tradeOnWeekends)]
    }
    setcolorder(dt, c("timestamp", "tradingday"))
    return(dt)
}


#' nodoc
#' @export
GetTradingDayFromAKShare = function(begin, marketType) {
    tz = GetTimezone(marketType)
    ak = reticulate::import("akshare")
    tradingdays = ak$tool_trade_date_hist_sina()
    tradingdays = as.Date(as.numeric(tradingdays$trade_date), origin = "1970-01-01")
    tradingdays = MakeTime(as.character(tradingdays), tz = tz)
    tradingdays = tradingdays[tradingdays >= MakeTime(begin, tz = tz)]

    cuttingMethod = Demeter::GetTradingDayCuttingMethod(marketType)
    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)
    dt = data.table(tradingday = tradingdays)

    if (cuttingMethod == 1) {
        dt[, timestamp := GetBeginOfTradingDayNoDS(tradingday, cuttingHour, tradeOnWeekends)]
    } else {
        # assume the last weekday of previous year is not public holiday.
        # if violated, add that holiday to the input csv to workaround
        prevDay = dt$tradingday[1] - days(1)
        while (weekdays(prevDay) %in% c("Saturday", "Sunday")) {
            prevDay = prevDay - days(1)
        }
        dt[, timestamp := GetBeginOfNextTradingDayNoDS(c(prevDay, tradingday[-.N]), cuttingHour, tradeOnWeekends)]
    }
    setcolorder(dt, c("timestamp", "tradingday"))
    return(dt)
}


#' nodoc
#' @export
GetDefaultFSTDataSource = function() {
    dataSource = GetFSTDataSource(GetFileManagerEntry()$fst$dataRoots[1])
    return(dataSource)
}