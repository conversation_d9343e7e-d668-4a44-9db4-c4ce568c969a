#' nodoc
#' @export
GetFileManagerEntry = function() {
    AssertNotNull(fileMgrEntry)
    return(fileMgrEntry)
}

#' nodoc
#' @export
GetDefaultFileManager = function(dataRoot) {
    AssertNotNull(fileMgrEntry)
    for (storageType in names(fileMgrEntry)) {
        fileMgr = fileMgrEntry[[storageType]]
        for (root in fileMgr$dataRoots) {
            if (normalizePath(dataRoot) == normalizePath(root)) {
                return(fileMgr)
            }
        }
    }
    ErrorLog("GetDefaultFileManager", "Not found FileManager for dataRoot: {dataRoot}")
    return(NULL)
}

#' nodoc
#' @export
InitFileManager = function(xmlContent) {
    config = list()
    values = ParseValues(xmlContent)
    dataStorages = ParseDataStorages(xmlContent)
    fileRules = ParseFileRules(xmlContent)

    for (storageType in names(dataStorages)) {
        path = dataStorages[[storageType]]$path
        fileType = dataStorages[[storageType]]$fileType
        defaultRule = fileRules[[storageType]]$defaultRule
        particularRules = fileRules[[storageType]]$particularRules

        # replace placeholder and create file manager
        path = ReplacePlaceholder(path, values)
        dataRoots = list.files(dirname(path), full.names = TRUE)
        dataRoots = dataRoots[grepl(basename(path), basename(dataRoots), perl = TRUE) & file.info(dataRoots)$isdir]
        config[[storageType]] = FileManager$new(storageType, dataRoots, fileType, defaultRule, particularRules)
    }
    fileMgrEntry <<- config
}

#' nodoc
#' @export
NewFileManager = function(storageType, dataRoot) {
    AssertNotNull(fileMgrEntry)
    fileMgr = fileMgrEntry[[storageType]]
    if (is.null(fileMgr)) {
        ErrorLog("NewFileManager", "Not found FileManager for storageType: {storageType}")
        return(NULL)
    }
    return(FileManager$new(
        storageType, dataRoot,
        fileMgr$fileType,
        fileMgr$defaultRule,
        fileMgr$particularRules
    ))
}

#' nodoc
ParseValues = function(xmlContent) {
    values = list()
    valuesNode = getNodeSet(xmlContent, "//Values")
    if (IsEmpty(valuesNode)) return(values)
    for (child in getNodeSet(valuesNode[[1]], "//Value")) {
        key = xmlGetAttr(child, "key")
        value = xmlGetAttr(child, "value")
        values[[key]] = value
    }
    return(values)
}

#' nodoc
ParseDataStorages = function(xmlContent) {
    dataStorages = list()
    dataStoragesNode = getNodeSet(xmlContent, "//DataStorages")
    if (IsEmpty(dataStoragesNode)) return(dataStorages)
    for (child in getNodeSet(dataStoragesNode[[1]], "//DataStorage")) {
        type = xmlGetAttr(child, "type")
        path = xmlGetAttr(child, "path")
        fileType = xmlGetAttr(child, "fileType")
        dataStorages[[type]] = list(path = path, fileType = fileType)
    }
    return(dataStorages)
}

#' nodoc
ParseFileRules = function(xmlContent) {
    fileRules = list()
    fileRulesNode = getNodeSet(xmlContent, "//FileRules")
    if (IsEmpty(fileRulesNode)) return(fileRules)
    for (child in getNodeSet(fileRulesNode[[1]], "//FileRule")) {
        isDefault = xmlGetAttr(child, "isDefault")
        storageType = xmlGetAttr(child, "storage")
        marketType = xmlGetAttr(child, "marketType")
        dataType = xmlGetAttr(child, "dataType")
        splitMethod = xmlGetAttr(child, "splitMethod")
        naming = xmlGetAttr(child, "naming")

        # validation check
        AssertNotNull(storageType)
        AssertNotNull(splitMethod)
        AssertNotNull(naming)
        if (naming == "time" && splitMethod == "no") {
            stop("splitMethod cannot be no for naming of time")
        }

        # create rule
        if (is.null(marketType)) marketType = ".*"
        if (is.null(dataType)) dataType = ".*"
        rule = list(marketType = marketType, dataType = dataType, splitMethod = splitMethod, naming = naming)

        # init file rule
        fileRule = fileRules[[storageType]]
        if (is.null(fileRule)) {
            fileRule = list(defaultRule = data.table(), particularRules = data.table())
        }

        # merge file rules
        isDefault = !is.null(isDefault) && as.logical(isDefault)
        colume = if (isDefault) "defaultRule" else "particularRules"
        fileRule[[colume]] = rbind(fileRule[[colume]], rule, fill = TRUE)
        fileRules[[storageType]] = fileRule
    }
    return(fileRules)
}

#==================================================================================================
# utility functions
#' nodoc
ReplacePlaceholder = function(str, vars) {
    list2env(vars, envir = environment())
    return(glue(str))
}

#' nodoc
GetFileSuffix = function(fileType) {
    if (fileType == "xz") {
        return("tar.xz")
    }
    return(fileType)
}

#' nodoc
GetSplitTime = function(date, splitMethod) {
    date = MakeTime(date)
    if (splitMethod == "day") {
        return(DateToStr(date))
    } else if (splitMethod == "month") {
        return(MonthToStr(date))
    } else if (splitMethod == "year") {
        return(YearToStr(date))
    } else  {
        NotImplemented()
    }
}

#' nodoc
GetSplitTimeFormat = function(splitMethod) {
    if (splitMethod == "day") {
        return("%Y-%m-%d")
    } else if (splitMethod == "month") {
        return("%Y-%m")
    } else if (splitMethod == "year") {
        return("%Y")
    } else {
        NotImplemented()
    }
}

#' nodoc
GetDatesForRead = function(marketType, splitMethod, begin = NULL, end = begin) {
    AssertNotNull(begin)
    AssertNotNull(end)

    tz = GetTimezone(marketType)
    begin = MakeTime(begin, tz)
    end = MakeTime(end, tz)
    Assert(begin <= end)

    if (splitMethod == "day") {
        cuttingHour = GetTradingDayCuttingHour(marketType)
        cuttingMethod = GetTradingDayCuttingMethod(marketType)
        tradeOnWeekends = GetTradeOnWeekends(marketType)

        b = GetTradingDayNoDS(begin, cuttingHour, cuttingMethod, tradeOnWeekends)
        e = GetTradingDayNoDS(end, cuttingHour, cuttingMethod, tradeOnWeekends)

        days = seq(b, e, by = as.numeric(days(1)))
        return(DoubleToTime(days, tz))

    } else if (splitMethod == "month") {
        months = seq(begin, end, by = as.numeric(days(25)))
        months = c(begin - days(3), months, end + days(3))
        monthStrs = MonthToStr(months)
        uniqueIdx = !duplicated(monthStrs)
        months = months[uniqueIdx]
        return(DoubleToTime(months, tz))

    } else if (splitMethod == "year") {
        years = seq(begin, end, by = as.numeric(days(360)))
        years = c(begin - days(3), years, end + days(3))
        yearStrs = YearToStr(years)
        uniqueIdx = !duplicated(yearStrs)
        years = years[uniqueIdx]
        return(DoubleToTime(years, tz))

    } else {
        NotImplemented()
    }
}

#' nodoc
GetCuttingPoints = function(begin, end, splitMethod, cuttingHour, cuttingMethod, tradeOnWeekends) {
    if (splitMethod == "day") {
        cuttingPoints = GetTradingDaySeqNoDS(begin, end, cuttingHour, tradeOnWeekends)

    } else if (splitMethod == "month") {
        cuttingPoints = GetTradingMonthSeqNoDS(begin, end, cuttingHour, cuttingMethod, tradeOnWeekends)

    } else if (splitMethod == "year") {
        cuttingPoints = GetTradingYearSeqNoDS(begin, end, cuttingHour, cuttingMethod, tradeOnWeekends)

    } else {
        NotImplemented()
    }
    return(cuttingPoints)
}

#' nodoc
IsSupportMultiSymbolOperation = function(marketType, dataType) {
    return((marketType == "ChinaStock" && startsWith(dataType, "Fundamental/")) ||
               (marketType == "ChinaStock" && dataType == "IndexConstituent") ||
               startsWith(dataType, "Misc/"))
}

#' nodoc
ValidateParameter = function(dataType) {
    if (startsWith(dataType, "Bar/")) {
        barSize = gsub("^Bar/", "", dataType)
        if (barSize == "") FatalLog("ValidateParameter", "Invalid bar size: {dataType}")
    } else if (startsWith(dataType, "Fundamental/")) {
        factor = gsub("^Fundamental/", "", dataType)
        if (factor == "") FatalLog("ValidateParameter", "Invalid factor: {dataType}")
    }
}

#' nodoc
PostprocessData = function(marketType, dataType, symbol, data) {
    if (!is.null(data)) {
        setattr(data, "marketType", marketType)
        setattr(data, "dataType", dataType)
        setattr(data, "symbol", symbol)

        if (startsWith(dataType, "Bar/")) {
            barSize = gsub("^Bar/", "", dataType)
            setattr(data, "class", c("Bar", "TimeSeries", class(data)))
            setattr(data, "barSize", BarSizeToSecond(barSize))

        } else if (startsWith(dataType, "Fundamental/")) {
            data = FilterDataByColumn(data, symbol, 1)
            if (!is.null(data)) {
                factor = gsub("^Fundamental/", "", dataType)
                setattr(data, "factor", factor)
            }
        }
    }
    return(data)
}

#==================================================================================================
# implementation of internal functions
#' nodoc
GetFileRule = function(marketType, dataType) {
    if (!IsEmpty(.self$particularRules)) {
        for (i in seq_len(nrow(.self$particularRules))) {
            rule = .self$particularRules[i]
            if (grepl(rule$marketType, marketType, perl = TRUE) && grepl(rule$dataType, dataType, perl = TRUE)) {
                return(rule)
            }
        }
    }
    return(.self$defaultRule)
}

#' nodoc
ListDataType = function(marketType, filter = NULL) {
    path = file.path(dataRoots, marketType)
    if (!dir.exists(path)) return(NULL)

    # Get base data types
    baseTypes = list.dirs(path, full.names = FALSE, recursive = FALSE)
    if (IsEmpty(baseTypes)) return(NULL)
    if (is.null(filter)) return(sort(baseTypes))

    # Expand data types
    if (!IsEmpty(.self$particularRules)) {
        rules = .self$particularRules[storageType == .self$storageType]
        for (i in seq_len(nrow(rules))) {
            rule = rules[i]
            if (!is.null(rule$marketType) && !grepl(rule$marketType, marketType, perl = TRUE)) next
            if (is.null(rule$dataType) || !grepl("/", rule$dataType)) next
            if (filter == sub("^\\^(.*?)/.*", "\\1", rule$dataType)) {
                if (rule$naming == "type") {
                    subTypes = file_path_sans_ext(list.files(file.path(path, filter)))
                    if (length(subTypes) > 0) return(paste0(filter, "/", subTypes))
                    return(NULL)
                } else {
                    subTypes = list.dirs(file.path(path, filter), full.names = FALSE, recursive = FALSE)
                    if (length(subTypes) > 0) return(paste0(filter, "/", subTypes))
                    return(NULL)
                }
            }
        }
    }
    return(filter)
}

#' nodoc
ConvertDataType = function(dataType) {
    formatDataType = dataType
    if (.self$storageType %in% c("solid", "solidArch")) {
        if (startsWith(dataType, "Misc/") && !startsWith(dataType, "Misc/Solid")) {
            formatDataType = sub("Misc/", "Misc/Solid", dataType)
        } else if (!startsWith(dataType, "Misc/") && !startsWith(dataType, "Solid")) {
            formatDataType = glue("Solid{dataType}")
        }
    }
    return(formatDataType)
}

#' nodoc
ListSymbolNamedData = function(marketType, dataType, BeginEndReadFunc = NULL) {
    dir = file.path(dataRoots, marketType, dataType)
    fileRule = GetFileRule(marketType, dataType)

    # list data splitted by day
    if (fileRule$splitMethod == "day") {
        tz = GetTimezone(marketType)
        cuttingHour = GetTradingDayCuttingHour(marketType)
        tradeOnWeekends = GetTradeOnWeekends(marketType)
        ret = ListDailySplitData(dir, fileType, tz, cuttingHour, tradeOnWeekends)
        ret = DataDetailToBrief(ret)
        return(ret)
    }
    ret = ListSplitData(dir, fileType, BeginEndReadFunc)
    return(ret)
}

#' nodoc
ListNonSymbolNamedData = function(marketType, dataType, ReadFunc = NULL) {
    if (startsWith(dataType, "Misc/")) {
        path = GetFilePath(marketType, dataType)
        info = ListMiscData(path, ReadFunc)
        return(info)

    } else if (dataType == "IndexConstituent") {
        path = file.path(dataRoots, marketType, dataType)
        files = list.files(path, full.names = TRUE)
        info = ListMiscData(files, FSTReadFunc)
        return(info)

    } else if (startsWith(dataType, "Fundamental/")) {
        factor = gsub("^Fundamental/", "", dataType)
        path = file.path(dataRoots, marketType, paste0(dataType, ".fst"))
        if (!file.exists(path)) return(NULL)
        data = read.fst(path)
        symbol = colnames(data)
        symbol = symbol[2:length(symbol)]
        symbol = sort(symbol)
        info = data.table(begin = first(data[[1]]), end = last(data[[1]]), symbol = symbol)
        setattr(info, "factor", factor)
        return(info)

    } else {
        NotImplemented()
    }
}

#==================================================================================================
# implementation of interface functions
#' nodoc
ListData = function(marketType = NULL, dataType = NULL, ReadFunc = NULL, BeginEndReadFunc = NULL) {
    if (is.null(marketType)) {
        marketTypes = list.dirs(dataRoots, full.names = FALSE, recursive = FALSE)
        marketTypes = setdiff(marketTypes, "InfoCache")
        return(marketTypes)
    }

    dataTypes = ListDataType(marketType, dataType)
    if (length(dataTypes) != 1 || is.null(dataType) || dataType != dataTypes) {
        return(dataTypes)
    }

    fileRule = GetFileRule(marketType, dataType)
    if (fileRule$naming == "symbol") {
        return(ListSymbolNamedData(marketType, dataType, BeginEndReadFunc))
    } else {
        return(ListNonSymbolNamedData(marketType, dataType, ReadFunc))
    }
}

#' nodoc
ReadData = function(marketType, dataType, symbol = NULL, begin = GetConfig(DefaultBegin), end = GetConfig(DefaultEnd), ReadFunc = NULL) {
    fileRule = .self$GetFileRule(marketType, dataType)
    if (fileRule$naming == "symbol") {
        if (is.null(symbol)) {
            FatalLog("ReadData", "Symbol is missing, required by file rule: {ListToStr(fileRule)}")
        } else if (length(symbol) != 1 && !IsSupportMultiSymbolOperation(marketType, dataType)) {
            FatalLog("ReadData", "Symbol is not unique, required by file rule: {ListToStr(fileRule)}")
        }
    }

    # read misc data
    if (startsWith(dataType, "Misc/")) {
        path = GetFilePath(marketType, dataType)
        return(ReadMiscData(path, symbol, begin, end, ReadFunc))
    }

    # read data without splitting
    if (fileRule$splitMethod == "no") {
        path = GetFilePath(marketType, dataType, symbol = symbol)
        return(FilterDataByTime(ReadFunc(path), begin, end))
    }

    # read data with splitting
    dates = GetDatesForRead(marketType, fileRule$splitMethod, begin, end)
    paths = GetFilePath(marketType, dataType, date = dates, symbol = symbol)
    data = ReadAndMerge(ReadFunc, paths, begin, end)
    if (!is.null(symbol) && fileRule$naming != "symbol") {
        symbols = symbol
        data = data[symbol %in% symbols]
    }
    return(data)
}

#' nodoc
WriteData = function(marketType, dataType, data, symbol = NULL, ReadFunc = NULL, WriteFunc = NULL) {
    fileRule = .self$GetFileRule(marketType, dataType)
    if (fileRule$naming == "symbol") {
        if (is.null(symbol)) {
            FatalLog("WriteData", "Symbol is missing, required by file rule: {ListToStr(fileRule)}")
        } else if (length(symbol) != 1 && !IsSupportMultiSymbolOperation(marketType, dataType)) {
            FatalLog("WriteData", "Symbol is not unique, required by file rule: {ListToStr(fileRule)}")
        }
    }

    # write misc data
    if (startsWith(dataType, "Misc/")) {
        path = GetFilePath(marketType, dataType)
        return(WriteMiscData(data, path, WriteFunc, ReadFunc))
    }

    # write data without splitting
    if (fileRule$splitMethod == "no") {
        path = GetFilePath(marketType, dataType, symbol = symbol)
        return(WriteFunc(data, path))
    }

    # write data with splitting
    tz = GetTimezone(marketType)
    cuttingHour = GetTradingDayCuttingHour(marketType)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    begin = StrToTime(first(data[[1]]), tz)
    end = StrToTime(last(data[[1]]), tz)

    cuttingPoints = GetCuttingPoints(begin, end, fileRule$splitMethod, cuttingHour, cuttingMethod, tradeOnWeekends)
    for (i in 1:(length(cuttingPoints) - 1)) {
        begin = cuttingPoints[i]
        end = cuttingPoints[i + 1]
        part = data[begin <= data[[1]] & data[[1]] < end]
        if (IsEmpty(part)) next

        date = switch(cuttingMethod, begin, end)
        path = GetFilePath(marketType, dataType, date = date, symbol = symbol)
        WriteFunc(part, path)
    }
    return()
}

#' nodoc
ListFiles = function(marketType, dataType, date = NULL, pathFilter = NULL, pattern = NULL, fullNames = FALSE, excludeDir = FALSE, sansExt = FALSE) {
    dirPath = GetDirPath(marketType, dataType, date, pathFilter)
    files = list.files(dirPath, pattern, full.names = fullNames)
    if (excludeDir) files = files[!file.info(files)$isdir]
    if (sansExt) files = file_path_sans_ext(files)
    return(files)
}

#' nodoc
ListDates = function(marketType, dataType, pathFilter = NULL, begin = NULL, end = NULL) {
    files = ListFiles(marketType, dataType, pathFilter = pathFilter, sansExt = TRUE)
    dates = files[which(IsDateFormatString(files))]
    dates = unique(dates)

    if (!is.null(begin)) {
        begin = MakeTime(begin)
        dates = dates[which(StrToDate(dates) >= begin)]
    }
    if (!is.null(end)) {
        end = MakeTime(end)
        dates = dates[which(StrToDate(dates) <= end)]
    }
    return(dates)
}

#' nodoc
GetFilePath = function(marketType, dataType, date = NULL, symbol = NULL, pathFilter = NULL) {
    filePaths = c()
    newDataType = .self$ConvertDataType(dataType)
    fileRule = .self$GetFileRule(marketType, dataType)
    for (filePath in .self$GetDirPath(marketType, dataType, NULL, pathFilter)) {
        # get time spilt
        if (fileRule$splitMethod != "no") {
            AssertNotNull(date)
            timeStr = GetSplitTime(date, fileRule$splitMethod)
            filePath = file.path(filePath, unique(timeStr))
        }
        # get file name
        fileSuffix = GetFileSuffix(.self$fileType)
        if (fileRule$naming == "time") {
            filePath = glue("{filePath}.{fileSuffix}")
        } else if (fileRule$naming == "symbol") {
            if (is.null(symbol)) ErrorLog("GetFilePath", "Symbol is missing, required by file rule: {ListToStr(fileRule)}")
            filePath = file.path(filePath, glue("{symbol}.{fileSuffix}"))
        } else if (fileRule$naming == "type") {
            filePath = file.path(filePath, basename(newDataType))
            filePath = glue("{filePath}.{fileSuffix}")
        } else {
            NotImplemented()
        }
        filePaths = c(filePaths, filePath)
    }
    return(filePaths)
}

#' nodoc
GetDirPath = function(marketType, dataType, date = NULL, pathFilter = NULL) {
    dataTypePaths = c()
    newDataType = .self$ConvertDataType(dataType)
    for (dataRoot in .self$dataRoots) {
        if (!is.null(pathFilter) && !grepl(pathFilter, dataRoot)) next
        fileRule = .self$GetFileRule(marketType, dataType)
        path = file.path(dataRoot, marketType, newDataType)
        if (fileRule$naming == "type") {
            path = dirname(path)
        } else if (!is.null(date) && fileRule$naming != "time") {
            timeStr = GetSplitTime(date, fileRule$splitMethod)
            path = file.path(path, timeStr)
        }
        dataTypePaths = c(dataTypePaths, path)
    }
    return(dataTypePaths)
}

#' nodoc
GetDataRoot = function(marketType, dataType) {
    result = c()
    for (dataRoot in .self$dataRoots) {
        files = .self$ListFiles(marketType, dataType, pathFilter = dataRoot)
        if (!IsEmpty(files)) {
            result = c(result, dataRoot)
        }
    }
    return(result)
}

#==================================================================================================
# facade
#' nodoc
#' @export
FileManager = setRefClass("FileManager",
    fields = c(
        "storageType",
        "dataRoots",
        "fileType",
        "defaultRule",
        "particularRules"
    ),
    methods = list(
        initialize = function(storageType, dataRoots, fileType, defaultRule, particularRules) {
            .self$storageType = storageType
            .self$dataRoots = dataRoots
            .self$fileType = fileType
            .self$defaultRule = defaultRule
            .self$particularRules = particularRules
        },

        # ====== interface functions ======
        ListData = ListData,
        ReadData = ReadData,
        WriteData = WriteData,
        ListFiles = ListFiles,
        ListDates = ListDates,
        GetFilePath = GetFilePath,
        GetDirPath = GetDirPath,
        GetDataRoot = GetDataRoot,

        # ====== internal functions ======
        GetFileRule = GetFileRule,
        ListDataType = ListDataType,
        ConvertDataType = ConvertDataType,
        ListSymbolNamedData = ListSymbolNamedData,
        ListNonSymbolNamedData = ListNonSymbolNamedData
    )
)
