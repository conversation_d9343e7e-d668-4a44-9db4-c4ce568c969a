# ==================================================================================================
# utilities
# static method

ParsePyDfWithNaT = function(df) {
    df = ParsePyDf(DealWithNaT(df))
    # 1. hack logic: "1970-01-01" represents NaT in df
    # 2. change list to vector in data.table
    convertNaTAndNUll = function(x) {
        if (is.POSIXct(x)) x[x == as.POSIXct(0, origin = "1970-01-01")] = NA
        if (is.list(x)) x = unlist(sapply(x, function(v) {
            return(ifelse(is.null(v), NA, v))
        }))
        return(x)
    }
    df[, names(df) := lapply(.SD, convertNaTAndNUll), .SDcols = names(df)]
    return(df)
}

GetDataTypeOfRQSupportMarketType = function(marketType) {
    map = list(
        ChinaStock = c("Bar", "Fundamental", "Misc/IndustryMap", "Misc/ProductInfo", "Misc/PxAdj", "Misc/PxLimit", "Misc/TradingDay"),
        ChinaETF =c("Bar", "Misc/PxLimit", "Misc/PxAdj", "Misc/TradingDay"),
        ChinaFuture = c("Tick", "Bar", "Misc/PxLimit", "Misc/Session", "Misc/TradingDay"),
        ChinaOption = c("Misc/PxLimit", "Misc/TradingDay"),
        ChinaBond = c("Bar", "Misc/TradingDay"),  # TODO find rq data that related to to ChinaBond
        ChinaCvtBond =c("Bar", "Misc/TradingDay"),
        ChinaGovBond =c("Bar", "Misc/TradingDay"),  # TODO find rq data that related to to ChinaGovBond
        ChinaPlgBond =c("Bar", "Misc/TradingDay"),
        ChinaFund = c("Bar", "Misc/PxLimit", "Misc/TradingDay")
    )

    if (marketType %in% names(map)) {
        return(map[[marketType]])
    }
    FatalLog("GetDataTypeOfRQSupportMarketType", "Unsupported market type: {marketType}")
}

GetRQSupportMarketType = function() {
    return(list(
        ChinaStock = c("CS", "INDX"),
        ChinaETF ="ETF",
        ChinaFuture = "Future",
        ChinaOption ="Option",
        ChinaBond = "Convertible",  # TODO find rq data that related to to ChinaBond
        ChinaCvtBond ="Convertible",
        ChinaGovBond ="Repo",  # TODO find rq data that related to to ChinaGovBond
        ChinaPlgBond ="Repo",
        ChinaFund = "LOF"
    ))
}

RQToRQMarketType = function(marketType) {
    rqSupportMarketType = GetRQSupportMarketType()
    if (marketType %in% names(rqSupportMarketType)) {
        return(rqSupportMarketType[[marketType]])
    }
    FatalLog("RQToRQMarketType", "Unsupported market type: {marketType}")
}

RQUpdateSymbolMaps = function(marketType, force) {
    if (!force && !is.null(symbolMaps[[marketType]])) {
        return()
    }
    obj = rq$all_instruments(type = ToRQMarketType(marketType))
    insts = ParsePyDf(obj)
    if (marketType == "ChinaFuture" || marketType == "ChinaOption") insts = insts[listed_date >= "2015-01-01"]

    symbol = RQSymbolToStandard(insts$type, insts$order_book_id)
    map = data.table(fullSymbol = insts$order_book_id, symbol = symbol)
    setorder(map, symbol)
    .self$symbolMaps[[marketType]] = map
}

RQGetFullSymbol = function(marketType, dataType, symbol) {
    UpdateSymbolMaps(marketType, FALSE)
    if (is.null(symbol)) {
        if (marketType == "ChinaStock" && (startsWith(dataType, "Fundamental/") || startsWith(dataType, "Misc/PxAdj"))) {
            fullSymbol = symbolMaps[[marketType]][!startsWith(symbol, "Idx"), fullSymbol]
        } else {
            fullSymbol = symbolMaps[[marketType]]$fullSymbol
        }
        return(fullSymbol)
    }

    symbolMap = symbolMaps[[marketType]]
    target = symbol
    fullSymbol = symbolMap[symbol %in% target, fullSymbol]
    return(fullSymbol)
}

# ==================================================================================================
# implementation
RQReadBar = function(api, marketType, symbol, barSize, begin, end) {
    obj = api$get_price(symbol, begin, end, barSize, adjust_type = "none", expect_df = TRUE)
    bar = ParsePyDf(obj)
    if (IsEmpty(bar)) {
        return(NULL)
    }

    ret = data.table(
        closetime = bar[[2]] - 8 * 3600,
        open = bar$open,
        high = bar$high,
        low = bar$low,
        close = bar$close,
        vol = bar$volume,
        value = bar$total_turnover
    )
    setattr(ret$closetime, "tzone", GetTimezone(marketType))
    setattr(ret, "barSize", BarSizeToSecond(barSize))
    setattr(ret, "class", c("Bar", "TimeSeries", class(ret)))
    return(ret)
}

RQReadTick = function(api, marketType, symbol, begin, end) {
    obj = api$get_price(symbol, begin, end, frequency = "tick", adjust_type = "none")
    tick = ParsePyDf(obj)

    if (IsEmpty(tick)) {
        return(NULL)
    }

    timestamp = tick$datetime - 8 * 3600
    setattr(timestamp, "tzone", GetTimezone(marketType))

    ret = data.table(
        timestamp = timestamp,
        bidpx = tick$b1,
        bidvol = tick$b1_v,
        askpx = tick$a1,
        askvol = tick$a1_v,
        lastpx = tick$last,
        totalvol = tick$volume,
        totalvalue = tick$total_turnover,
        openinterest = tick$open_interest
    )
    return(ret)
}

RQReadFundamental = function(api, marketType, factor, symbol, begin, end) {
    if (marketType != "ChinaStock") {
        FatalLog("RQReadFundamental", "Only ChinaStock is supported right now")
    }

    obj = NULL
    e = try_capture_stack(
        {
            tz = GetTimezone(marketType)
            if (factor == "is_st") {
                obj = api$is_st_stock(symbol, start_date = begin, end_date = end)
            } else {
                obj = api$get_factor(symbol, factor, start_date = begin, end_date = end)
            }
        },
        environment()
    )

    OnException(e)

    if (PyIsNone(obj)) {
        return(NULL)
    } else if (is(obj, "python.builtin.str")) {
        write(py_to_r(obj), stderr())
        return(NULL)
    } else if (is(obj, "pandas.core.series.Series") && length(symbol) > 1) {
        values = ParsePyDf(obj)
        timestamp = StrToDate(names(values)[2], tz)
        values = transpose(values)
        names(values) = gsub("\\..*", "", values[1, ])
        values = values[2, ]
        values = cbind(timestamp = timestamp, values)
    } else {
        values = ParsePyDf(obj)
        if (factor == "is_st") {
            col.names = gsub("\\..*", "", names(values))
            col.names[1] = "timestamp"
            values[[1]] = StrToDate(strftime(values[[1]], tz = tz), tz)
            names(values) = col.names
        } else {
            timestamp = StrToDate(strftime(unique(values[[2]]), tz = tz), tz)
            splitValues = split(values[, c(1, 3)], by = names(values)[1], keepBy = F)
            for (sym in names(splitValues)) {
                names(splitValues[[sym]]) = sym
            }

            values = as.data.table(splitValues)
            names(values) = gsub("\\..*", "", names(values))
            values = cbind(timestamp = timestamp, values)
        }
    }

    ret = replace(values, is.na(values), NA)
    shift.time = ifelse(factor == "is_st", 8, 16) * 60 * 60
    ret = ret[, timestamp := DoubleToTime(timestamp + shift.time, tz)]

    setattr(ret, "factor", factor)
    return(ret)
}

RQReadMiscPxAdj = function(api, marketType, symbol, begin, end) {
    if (!marketType %in% c("ChinaStock", "ChinaETF")) {
        FatalLog("RQReadMiscPxAdj", "Only ChinaStock and ChinaETF are supported right now")
    }

    obj = api$get_ex_factor(symbol, begin, end)
    values = ParsePyDfWithNaT(obj)
    if (IsEmpty(values)) {
        return(NULL)
    }

    tz = GetTimezone(marketType)
    date = StrToDate(strftime(values$ex_date, tz = tz), tz)

    ret = data.table(
        timestamp = date,
        symbol = gsub("\\..*", "", values$order_book_id),
        adjustment = values$ex_factor
    )

    ret = ret[order(timestamp, symbol)][, timestamp := DoubleToTime(timestamp + 8 * 60 * 60, tz)]
    ret[]
    return(ret)
}

RQReadSession = function(api, marketType, begin, end) {
    if (!marketType %in% c("ChinaFuture", "ChinaOption")) {
        FatalLog("RQReadSession", "Only support ChinaFuture and ChinaOption market")
    }

    tz = GetTimezone(marketType)
    tradingDay = api$get_trading_dates(begin, end, "cn")
    tradingDay = py_to_r(tradingDay)
    tradingDay = as.Date(unlist(tradingDay), tz = tz, origin = "1970-01-01")

    myallInst = list()
    for (day in format(tradingDay)) {
        InfoLog("RQReadSession", "Getting allInstruments: ", day)
        rqMarket = RQToRQMarketType(marketType)
        allInstrument = api$all_instruments(type = rqMarket, date = day)
        allInstrument = ParsePyDf(allInstrument)
        if (IsEmpty(allInstrument)) next

        # exclude contracts whose maturity_date is "0000-00-00"
        allInstrument = allInstrument[maturity_date != "0000-00-00"]

        if (marketType == "ChinaOption") {
            allInstrument = allInstrument[exchange != "XSHE" & exchange != "XSHG"]
        }
        allInstrument[, underlying_symbol := ifelse(exchange %in% c("SHFE", "DCE", "INE", "GFEX"),
            tolower(underlying_symbol), underlying_symbol
        )]

        allInst = split(allInstrument, by = "underlying_symbol")
        if (length(allInst) == 0) next

        for (i in seq_along(allInst)) {
            inst = names(allInst)[i]
            data = allInst[[i]]
            setorder(data, maturity_date)
            mySymbol = first(data$order_book_id)
            if (inst %in% names(myallInst)) {
                if (last(myallInst[[inst]]$symbol) != mySymbol) {
                    myallInst[[inst]]$symbol = c(myallInst[[inst]]$symbol, mySymbol)
                    myallInst[[inst]]$time = c(myallInst[[inst]]$time, as.Date(day, tz = tz))
                }
            } else {
                myallInst[[inst]] = list(symbol = mySymbol, time = as.Date(day, tz = tz))
            }
        }
    }

    GetTrainingDayHours = function(symbol, day) {
        return(py_to_r(api$get_trading_hours(symbol, day, frequency = "tick")))
    }

    instTradingHours = data.table()
    for (i in seq_along(myallInst)) {
        inst = names(myallInst)[i]
        instSymbol = myallInst[[i]]
        InfoLog("RQReadSession", "# ", i, "/", length(myallInst), " Getting sessions: ", inst)

        validTradingDay = tradingDay[which(tradingDay == first(instSymbol$time)):length(tradingDay)]

        tradingHours = c()
        timestamp = c()
        for (i in seq_along(validTradingDay)) {
            day = validTradingDay[i]
            symbol = instSymbol$symbol[findInterval(as.Date(day, tz = tz), instSymbol$time)]
            if (is.na(symbol)) FatalLog("RQReadSession", "Failed to find symbol in instrument")

            tradingDayHours = GetTrainingDayHours(symbol, day)
            if (!is.null(tradingDayHours)) {
                tradingHours = c(tradingHours, tradingDayHours)
                timestamp = c(timestamp, day)
            }
        }

        if (length(tradingHours) > 0) {
            class(timestamp) = "Date"
            value = data.table(timestamp = MakeTime(timestamp), derivname = inst, tradinghours = tradingHours)
            instTradingHours = rbind(instTradingHours, value)
        }
    }

    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)

    setattr(instTradingHours$timestamp, "tzone", GetTimezone(marketType))
    setattr(instTradingHours, "marketType", marketType)
    setattr(instTradingHours, "dataType", "Misc/Session")
    instTradingHours[, timestamp := GetBeginOfTradingDayNoDS(timestamp, cuttingHour, tradeOnWeekends)]
    setorder(instTradingHours, timestamp, derivname)

    return(instTradingHours)
}

RQReadMiscIndustryMap = function(api, marketType, symbol, begin, end) {
    if (marketType != "ChinaStock") {
        FatalLog("RQReadMiscIndustryMap", "Only ChinaStock is supported right now")
    }

    tradingDay = api$get_trading_dates(begin, end, "cn")
    tradingDay = py_to_r(tradingDay)

    GetIndustry = function(symbol, day) {
        industry = api$get_instrument_industry(symbol, date = day, source = "citics", level = 0)
        return(ParsePyDf(industry))
    }
    FindFirstDiffData = function(symbol, tradingDay, low, high, value) {
        diffData = NULL
        while (low <= high) {
            mid = as.integer((low + high) / 2)
            day = tradingDay[[mid]]
            targetValue = GetIndustry(symbol, day)
            if (is.null(targetValue)) {
                high = mid - 1
            } else if (is.character(all.equal(value, targetValue, check.attributes = FALSE))) {
                high = mid - 1
                diffData = list(index = mid, day = day, industry = targetValue)
            } else {
                low = mid + 1
            }
        }
        return(diffData)
    }

    ret = c()
    for (sym in symbol) {
        firstIndustry = GetIndustry(sym, first(tradingDay))
        if (is.null(firstIndustry)) next
        lastIndustry = GetIndustry(symbol, last(tradingDay))

        industry = rbind(firstIndustry)
        date = c(first(tradingDay))
        if (is.null(lastIndustry) || is.character(all.equal(firstIndustry, lastIndustry, check.attributes = FALSE))) {
            low = 1
            high = length(tradingDay)
            while (!is.null(diffData <- FindFirstDiffData(sym, tradingDay, low, high, last(industry)))) {
                low = diffData$index
                date = c(date, diffData$day)
                industry = rbind(industry, diffData$industry)
            }
        }
        myIndustry = cbind(timestamp = date, industry)
        ret = rbind(ret, myIndustry)
    }

    tz = GetTimezone(marketType)
    colnames(ret)[2] = "symbol"
    ret[, symbol := gsub("\\..*", "", symbol)]
    ret = ret[order(timestamp, symbol)][, timestamp := DoubleToTime(as.POSIXct(timestamp) + 8 * 60 * 60, tz)]
    return(ret)
}

RQReadPriceLimit = function(api, symbolMaps, marketType, symbol, begin, end) {
    tradingDay = api$get_trading_dates(begin, end, "cn")
    tradingDay = py_to_r(tradingDay)
    obj = api$get_price(symbol, start_date = first(tradingDay), end_date = last(tradingDay), fields = c("limit_up", "limit_down"), adjust_type = "none", expect_df = TRUE)
    ret = ParsePyDf(obj)
    if (IsEmpty(ret)) {
        return(NULL)
    }

    ret = ret[, order_book_id := symbolMaps[[marketType]][match(order_book_id, fullSymbol), symbol]]

    names(ret) = c("symbol", "timestamp", "highlimit", "lowlimit")

    tz = GetTimezone(marketType)
    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)

    setcolorder(ret, "timestamp")
    setorder(ret, timestamp, symbol)
    setattr(ret$timestamp, "tzone", tz)

    ret[, timestamp := GetBeginOfTradingDayNoDS(timestamp, cuttingHour, tradeOnWeekends)]
    # remove invalid symbol
    ret = ret[!is.na(symbol)]

    return(ret)
}

RQReadTradingDay = function(api, marketType, begin, end) {
    obj = api$get_trading_dates(begin, end, "cn")
    tradingDay = py_to_r(obj)

    if (IsEmpty(tradingDay)) {
        return(NULL)
    }

    tradingDay = DoubleToTime(sapply(tradingDay, function(x) as.POSIXct(x))) - hours(8)
    setattr(tradingDay, "tzone", GetTimezone(marketType))
    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    begin = GetBeginOfTradingDayNoDS(tradingDay, cuttingHour, tradeOnWeekends)
    ret = data.table(timestamp = begin, tradingday = tradingDay)
    return(ret)
}

RQReadData = function(marketType, dataType, symbol, begin = GetConfig(DefaultBegin), end = GetConfig(DefaultEnd)) {
    tz = GetTimezone(marketType)

    if (!Connect()) return(NULL)

    fullSymbol = GetFullSymbol(marketType, dataType, symbol)
    if (is.null(fullSymbol)) FatalLog("RQReadData", "Unknown symbol: {symbol}")
    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    dateBegin = DateToStr(GetTradingDayNoDS(begin, cuttingHour, cuttingMethod, tradeOnWeekends))
    dateEnd = DateToStr(GetTradingDayNoDS(end, cuttingHour, cuttingMethod, tradeOnWeekends))

    if (marketType == "ChinaStock") {
        if (startsWith(dataType, "Bar/")) {
            barSize = as.integer(gsub("^Bar/", "", dataType))
            if (barSize == 0) FatalLog("RQReadData", "Invalid bar size: {dataType}")
            rqBarSize = paste0(barSize / 60, "m")
            data = RQReadBar(rq, marketType, fullSymbol, rqBarSize, dateBegin, dateEnd)
        } else if (startsWith(dataType, "Fundamental/")) {
            factor = gsub("^Fundamental/", "", dataType)
            data = RQReadFundamental(rq, marketType, factor, fullSymbol, dateBegin, dateEnd)
            data = FilterDataByColumn(data, symbol, 1)
        } else if (dataType == "Misc/PxAdj") {
            data = RQReadMiscPxAdj(rq, marketType, fullSymbol, dateBegin, dateEnd)
        } else if (dataType == "Misc/IndustryMap") {
            data = RQReadMiscIndustryMap(rq, marketType, fullSymbol, dateBegin, dateEnd)
        } else if (dataType == "Misc/PxLimit") {
            data = RQReadPriceLimit(rq, symbolMaps, marketType, fullSymbol, dateBegin, dateEnd)
        } else if (dataType == "Misc/TradingDay") {
            data = RQReadTradingDay(rq, marketType, dateBegin, dateEnd)
        } else {
            NotImplemented()
        }
    } else if (marketType == "ChinaFuture") {
        if (dataType == "Tick") {
            data = RQReadTick(rq, marketType, fullSymbol, dateBegin, dateEnd)
        } else if (startsWith(dataType, "Bar/")) {
            barSize = as.integer(gsub("^Bar/", "", dataType))
            if (barSize == 0) FatalLog("RQReadData", "Invalid bar size: {dataType}")

            rqBarSize = paste0(barSize / 60, "m")
            data = RQReadBar(rq, marketType, fullSymbol, rqBarSize, dateBegin, dateEnd)
        } else if (dataType == "Misc/PxLimit") {
            data = RQReadPriceLimit(rq, symbolMaps, marketType, fullSymbol, dateBegin, dateEnd)
        } else if (dataType == "Misc/TradingDay") {
            data = RQReadTradingDay(rq, marketType, dateBegin, dateEnd)
        } else if (dataType == "Misc/Session") {
            data = RQReadSession(rq, marketType, dateBegin, dateEnd)
        } else {
            NotImplemented()
        }
    } else if (marketType == "ChinaOption" ||
        marketType %in% c("ChinaBond", "ChinaCvtBond", "ChinaGovBond", "ChinaPlgBond", "ChinaETF", "ChinaFund")) {
        if (dataType == "Misc/TradingDay") {
            data = RQReadTradingDay(rq, marketType, dateBegin, dateEnd)
        } else if (startsWith(dataType, "Bar/")) {
            barSize = as.integer(gsub("^Bar/", "", dataType))
            if (barSize == 0) FatalLog("RQReadData", "Invalid bar size: {dataType}")
            rqBarSize = paste0(barSize / 60, "m")
            data = RQReadBar(rq, marketType, fullSymbol, rqBarSize, dateBegin, dateEnd)
        } else if (dataType == "Misc/PxLimit") {
            data = RQReadPriceLimit(rq, symbolMaps, marketType, fullSymbol, dateBegin, dateEnd)
        } else if (dataType == "Misc/PxAdj") {
            data = RQReadMiscPxAdj(rq, marketType, fullSymbol, dateBegin, dateEnd)
        } else {
            NotImplemented()
        }
    }

    data = FilterDataByTime(data, begin, end)
    if (is.null(data)) {
        return(NULL)
    }

    setattr(data, "marketType", marketType)
    setattr(data, "dataType", dataType)
    setattr(data, "symbol", symbol)

    return(data)
}

RQListData = function(marketType = NULL, dataType = NULL) {
    if (!Connect()) return(NULL)

    if(is.null(marketType)) {
        return(names(GetRQSupportMarketType()))
    }

    if(is.null(dataType)) {
        return(GetDataTypeOfRQSupportMarketType(marketType))
    }

    cacheName = paste0(marketType, "/", dataType)
    if (!is.null(infoCache[[cacheName]])) {
        return(infoCache[[cacheName]])
    }

    if (marketType == "ChinaFuture" || marketType == "ChinaOption") {
        UpdateSymbolMaps(marketType, TRUE)
        symbol = symbolMaps[[marketType]]$symbol
        beginDay = "2015-01-01"
    } else if (marketType == "ChinaStock" ||
        marketType %in% c("ChinaBond", "ChinaCvtBond", "ChinaGovBond", "ChinaPlgBond", "ChinaETF", "ChinaFund")) {
        beginDay = "2000-01-01"
        if (dataType == "Fundamental") {
            obj = rq$get_all_factor_names()
            symbol = py_to_r(obj)
            symbol = symbol[-grep(".*_lyr.*", symbol)]
            symbol = symbol[-grep(".*_mrq.*", symbol)]
            symbol = symbol[-grep(".*_ttm.*", symbol)]
            symbol = c(symbol, "is_st")
        } else {
            UpdateSymbolMaps(marketType, TRUE)
            symbol = symbolMaps[[marketType]]$symbol
            if (startsWith(dataType, "Fundamental/") ||
                startsWith(dataType, "Misc/PxAdj") ||
                startsWith(dataType, "Misc/IndustryMap")) {
                symbol = symbol[!startsWith(symbol, "Idx")]
            }
        }
    } else {
        NotImplemented()
    }

    if (is.null(symbol) || length(symbol) == 0) {
        return(NULL)
    }
    tz = GetTimezone(marketType)
    info = data.table(begin = StrToDate(beginDay, tz), end = GetNow(tz), symbol = symbol)
    infoCache[[cacheName]] <<- info

    return(info)
}

RQSupportMultiSymbolOperation = function(marketType, dataType) {
    return((marketType == "ChinaStock" && startsWith(dataType, "Fundamental/")) || startsWith(dataType, "Misc/"))
}

RQInvalidateCache = function() {
    infoCache <<- list()
}

RQConnect = function() {
    if (isConnected) return(TRUE)

    e = try_capture_stack(
        {
            rq$init()
            .self$isConnected = TRUE
            return(TRUE)
        },
        environment()
    )

    OnException(e, "Failed to init rq server.")

    return(isConnected)
}

RQDisconnect = function() {
    rq$reset()
    .self$isConnected = FALSE
    return(invisible())
}

# ==================================================================================================
# facade
#' nodoc
#' @export
GetRQDataSource = function() {
    return(RQDataSourceGenerator$new())
}

#' nodoc
#' @export
RQDataSourceGenerator = setRefClass("RQDataSource",
    contains = "DataSource",
    fields = c("rq", "isConnected", "symbolMaps", "infoCache"),
    methods = list(
        initialize = function() {
            .self$rq = reticulate::import("rqdatac", convert = FALSE)
            .self$symbolMaps = list()
            .self$infoCache = list()
            .self$isConnected = FALSE
            pyUtilFile = system.file("Python", "PyUtil.py", package = "Demeter")
            source_python(pyUtilFile, envir = globalenv(), convert = F)
        },
        # ===== interface functions ======
        ReadData = RQReadData,
        WriteData = function(data) {
            FatalLog("RQDataSourceGenerator", "RQ data source is readonly")
        },
        ListData = RQListData,
        SupportMultiSymbolOperation = RQSupportMultiSymbolOperation,
        InvalidateCache = RQInvalidateCache,
        GetDesc = function() { return("<RQDataSource>") },

        # ===== other functions ==========
        Connect = RQConnect,
        Disconnect = RQDisconnect,
        UpdateSymbolMaps = RQUpdateSymbolMaps,
        GetFullSymbol = RQGetFullSymbol,
        ToRQMarketType = RQToRQMarketType
    )
)
