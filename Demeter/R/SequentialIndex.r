#' nodoc
#' @export
NewSequentialIndex = function(timestamp, marketType, symbol, ...) {
    seqIdx = data.table(
        timestamp = timestamp,
        marketType = marketType,
        symbol = symbol
    )
    seqIdx = ModifyDataTableByVarArgs(seqIdx, ...)
    return(seqIdx)
}

#' nodoc
#' @export
CalcSequentialPxAdj = function(sequentialIndex, dataSource) {
    if (IsEmpty(sequentialIndex)) {
        return(NULL)
    }
    symbol = sequentialIndex$symbol
    timestamp = sequentialIndex$timestamp
    marketType = sequentialIndex$marketType
    dataType = sequentialIndex$dataType
    if (is.null(dataType)) dataType = GetDefaultDataType(marketType)

    adj = sapply(2:nrow(sequentialIndex), function(i) {
        end = timestamp[i]
        begin = AddTradingDays(end, -1, dataSource, marketType[i])

        preData = dataSource$ReadData(marketType[i - 1], dataType[i - 1], symbol[i - 1], begin, end)
        preData = FilterData(dataSource, preData)
        prePriceCol = GetDefaultPriceCol(marketType[i - 1], dataType[i - 1])
        prePx = last(preData[[prePriceCol]])

        curData = dataSource$ReadData(marketType[i], dataType[i], symbol[i], begin, end)
        curData = FilterData(dataSource, curData)
        priceCol = GetDefaultPriceCol(marketType[i], dataType[i])
        curPx = last(curData[[priceCol]])

        return(prePx / curPx)
    })

    return(c(1, adj))
}

#' nodoc
#' @export
GetSequentialIndex = function(sequentialIndex,
                              dataSource,
                              begin = GetConfig(DefaultBegin),
                              end = GetConfig(DefaultEnd),
                              adjPx = TRUE,
                              filterData = TRUE,
                              TransformFunc = NULL) {
    if (IsEmpty(sequentialIndex)) {
        return(NULL)
    }

    begin = MakeTime(begin)
    end = MakeTime(end)
    timestamp = sequentialIndex$timestamp
    sequentialIndex = sequentialIndex[findInterval(begin, timestamp):findInterval(end, timestamp)]

    if (IsEmpty(sequentialIndex)) {
        InfoLog("GetSequentialIndex", "Sequential Index has no data between time ", TimeToStr(begin), " ~ ", TimeToStr(end))
        return(NULL)
    }

    timestamp = sequentialIndex$timestamp
    symbol = sequentialIndex$symbol
    marketType = sequentialIndex$marketType
    dataType = sequentialIndex$dataType
    adj = sequentialIndex$adj
    adjFuncV = sequentialIndex$adjFunc

    if (!adjPx) adj = rep(1, nrow(sequentialIndex))

    AssertNotNull(adj)
    Assert(!is.unsorted(timestamp, strictly = TRUE))
    if (is.null(dataType)) dataType = GetDefaultDataType(marketType)

    cumAdj = c(rev(cumprod(rev(1 / adj[-1]))), 1)
    beginV = c(begin, timestamp[-1])
    endV = c(timestamp[-1], end)

    finalData = data.table()
    for (i in seq_along(beginV)) {
        data = dataSource$ReadData(marketType[i], dataType[i], symbol[i], beginV[i], endV[i])
        if (filterData) data = FilterData(dataSource, data)
        if (!is.null(TransformFunc)) data = TransformFunc(data)
        if (IsEmpty(data)) next
        adjFunc = ifelse(is.null(adjFuncV[i]), GetAdjDataFunc(dataType[i]), adjFuncV[i])
        if (is.null(adjFunc)) {
            priceCol = GetDefaultPriceCol(marketType[i], dataType[i])
            timeCol = GetDefaultTimeCol(marketType[i], dataType[i])
            lastpx = data[[priceCol]] * cumAdj[i]
            data = data[, ..timeCol]
            colnames(data) = "timestamp"
            data[, `:=`(symbol = symbol[i], lastpx = lastpx)]
            finalData = rbind(finalData, data)
        } else {
            data = adjFunc(data, cumAdj[i])
            data[, symbol := symbol[i]]
            finalData = rbind(finalData, data)
        }
    }
    return(finalData)
}
