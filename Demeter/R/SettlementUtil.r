#' nodoc
#' @export
GetLatestMargin = function(dataSource, marketType, symbol) {
    mkt = marketType
    symDerivName = GetDerivNameOfSymbol(marketType, symbol)
    derivNameIdx = which(symDerivName == symbol)

    if (length(derivNameIdx) > 0) {
        prods = GetLoadedProducts()[marketType == mkt]
        for (idx in rev(derivNameIdx)) {
            derivSymbol = prods[derivativeName == symDerivName[idx], symbol]
            symbol = c(head(symbol, idx - 1), derivSymbol, tail(symbol, - idx))
        }
    }

    ret = data.table()
    for (sym in symbol) {

        dataType = GetDefaultDataType(mkt)
        pxCol = GetDefaultPriceCol(mkt)
        if (is.null(dataType) || is.null(pxCol)) next

        observeTime = GetNow()
        cuttingHour = GetTradingDayCuttingHour(marketType)
        cuttingMethod = GetTradingDayCuttingMethod(marketType)
        tradeOnWeekends = GetTradeOnWeekends(marketType)

        tradingDay = GetTradingDayNoDS(observeTime, cuttingHour, cuttingMethod, tradeOnWeekends)
        tradingDay = AddTradingDays(tradingDay, -1, dataSource, marketType)
        begin = GetBeginOfTradingDayNoDS(tradingDay, cuttingHour, tradeOnWeekends)

        data = dataSource$ReadData(marketType, dataType, sym, begin)
        data = FilterData(dataSource, data)
        px = last(data[[pxCol]])

        lastMargin = last(dataSource$ReadData(marketType, "Misc/Margin", sym))
        marginRate = lastMargin$longrate
        contractSize = GetContractSize(marketType, sym)
        margin = px * contractSize * marginRate
        margin = ifelse(length(margin) == 0, NA, margin)
        value = data.table(timestamp = begin, symbol = sym, margin = margin, longrate = marginRate, shortRate = lastMargin$shortrate)
        ret = rbind(ret, value, fill = TRUE)
    }

    return(ret)
}

#' nodoc
#' @export
GetCommissionOnPx = function(commission, dataSource, observeTime) {
    if (IsEmpty(commission)) return(NULL)

    marketType = GetMarketType(commission)
    AssertNotNull(marketType)
    normal = 0
    takeOnPx = NA
    closeToday = 0
    if (commission$rate != 0 || commission$closetodayrate != 0) {

        dataType = GetDefaultDataType(marketType)
        pxCol = GetDefaultPriceCol(marketType)
        AssertNotNull(dataType)
        AssertNotNull(pxCol)

        cuttingHour = GetTradingDayCuttingHour(marketType)
        cuttingMethod = GetTradingDayCuttingMethod(marketType)
        tradeOnWeekends = GetTradeOnWeekends(marketType)

        tradingDay = GetTradingDayNoDS(observeTime, cuttingHour, cuttingMethod, tradeOnWeekends)
        tradingDay = AddTradingDays(tradingDay, -1, dataSource, marketType)
        begin = GetBeginOfTradingDayNoDS(tradingDay, cuttingHour, tradeOnWeekends)

        data = dataSource$ReadData(marketType, dataType, commission$symbol, begin)
        data = FilterData(dataSource, data)

        if (IsEmpty(data)) {
            WarningLog("GetCommissionOnPx", "No market data for {commission$symbol} from time: {begin}")
        }

        px = last(data[[pxCol]])

        if (is.null(px)) {
            normal = closeToday = takeOnPx = NA
        } else {
            if (IsUBasedCryptoSymbol(marketType, commission$symbol)) px = 1
            normal = commission$rate * px
            if (!IsEmpty(commission$takerate * px))
                takeOnPx = commission$takerate * px
            closeToday = commission$closetodayrate * px
        }
    }

    if (commission$pershare != 0 || commission$closetodaypershare != 0) {
        contractSize = GetContractSize(marketType, commission$symbol)
        normal = normal + commission$pershare / contractSize
        takeOnPx = takeOnPx + commission$pershare / contractSize
        closeToday = closeToday + commission$closetodaypershare / contractSize
    }

    ret = data.table(
        timestamp = commission$timestamp,
        symbol = commission$symbol,
        onPx = normal,
        closeTodayOnPx = closeToday,
        rate = commission$rate,
        perShare = commission$pershare,
        closeTodayRate = commission$closetodayrate,
        closeTodayPerShare = commission$closetodaypershare
    )

    if (!is.na(takeOnPx)) ret[, takeOnPx := takeOnPx]
    return(ret)
}

#' nodoc
#' @export
GetLatestCommission = function(dataSource, marketType, symbol) {
    symDerivName = GetDerivNameOfSymbol(marketType, symbol)
    derivNameIdx = which(symDerivName == symbol)
    if (length(derivNameIdx) > 0) {
        mkt = marketType
        prods = GetLoadedProducts()[marketType == mkt]
        for (idx in rev(derivNameIdx)) {
            derivSymbol = prods[derivativeName == symDerivName[idx], symbol]
            symbol = c(head(symbol, idx - 1), derivSymbol, tail(symbol, - idx))
        }
    }

    ret = rbindlist(lapply(symbol, function(sym) {
        cms = last(dataSource$ReadData(marketType, "Misc/Commission", sym))
        if (IsEmpty(cms)) {
            WarningLog("GetLatestCommission", "No commission data: {sym}")
            return(NULL)
        } else {
            return(GetCommissionOnPx(cms, dataSource, GetNow()))
        }
    }))

    return(ret)
}

#' nodoc
#' @export
GetPositionMargin = function(dataSource, marketType, symbol, pos) {
    Assert(length(symbol) > 0 && length(marketType) > 0 && length(pos) > 0)
    Assert(length(symbol) == length(pos))

    if (length(marketType) == 1) {
        marketType = rep(marketType, length(symbol))
    } else if (length(marketType) != length(symbol)) {
        FatalLog("GetPositionMargin", "Length of marketType & symbol incompatible: {length(marketType)} {length(symbol)}")
    }

    marketTypeV = unique(marketType)
    totalMargin = 0

    for (mkt in marketTypeV) {
        mktIdx = which(marketType == mkt)
        mktSym = symbol[mktIdx]
        mktPos = pos[mktIdx]
        mktSymMargin = GetLatestMargin(dataSource, mkt, mktSym)

        derivName = GetDerivNameOfSymbol(mkt, mktSym)
        derivNameV = unique(derivName)

        for (der in derivNameV) {
            longMargin = 0
            shortMargin = 0
            for (i in which(derivName == der)) {
                symMargin = mktSymMargin[symbol == mktSym[i], margin]
                symPos = mktPos[i]
                if (symPos > 0) longMargin = longMargin + symMargin * symPos
                if (symPos < 0) shortMargin = shortMargin + symMargin * abs(symPos)
            }
            totalMargin = totalMargin + max(longMargin, shortMargin)
        }
    }

    return(totalMargin)
}

#' nodoc
#' @export
GetLatestTradingSession = function(marketType, symbol) {
    ret = lapply(symbol, function(sym) {
        ss = GetSession(GetNow(), marketType, sym)[[1]]
        if (IsEmpty(ss)) {
            WarningLog("GetLatestTradingSession", "No session data: {sym}")
            return(NULL)
        } else {
            return(ss)
        }
    })
    names(ret) = symbol
    return(ret)
}