#' Get downloaded index list from raw data root on dates
#' @export
GetCSVIndices = function(rawDataRoot, dates) {
    indicesToCalc = rbindlist(lapply(dates, \(date) {
        p = file.path(rawDataRoot, date)
        if (!dir.exists(p)) {
            InfoLog(componentName, "Local CSV not found on Date {date}")
            return(NULL)
        }
        files = list.files(p, pattern = "Idx.*\\.csv$")
        indices = gsub("(Idx.*)\\.csv", "\\1", files)
        csvIndices = data.table(timestamp = MakeTime(date), symbol = indices)
        return(csvIndices)
    }))
    return(indicesToCalc)
}


#' Get local csv indices whose raw data has been downloaded.
#' @export
GetLocalCSIndices = function(rawDataRoot, lastUpdateDate) {
    dirs = list.dirs(rawDataRoot, full.names = FALSE, recursive = FALSE)
    if (IsEmpty(dirs)) {
        return(data.table())
    }
    csvDates = MakeTime(dirs)
    csvDates = csvDates[csvDates > lastUpdateDate]
    indicesToCalc = GetCSVIndices(rawDataRoot, csvDates)
    return(indicesToCalc)
}


#' Get local csv indices that need to be calculated. (or that not in fst data)
#' @export
GetLocalCSIndicesNeedCalc = function(rawDataRoot, calcLocalCSV, existingData, cuttingHour, tradeOnWeekends) {
    indicesInRaw = rbindlist(lapply(calcLocalCSV, \(localCSVDate) GetCSVIndices(rawDataRoot, localCSVDate)))
    indicesInFST = copy(indicesInRaw)
    indicesInFST$timestamp = GetBeginOfNextTradingDayNoDS(indicesInFST$timestamp, cuttingHour, tradeOnWeekends) # The date of data in FSTData is the next trading day of the date of raw data.
    indicesNotInFST = indicesInFST[!existingData, on = .(symbol, timestamp)] # skip the indices already calculated in FST
    indicesToCalc = indicesInRaw[symbol %in% indicesNotInFST$symbol]
    return(indicesToCalc)
}


#' Check if this index need to be skipped.
#' @export
CheckCSIndexNeedSkipped = function(constituent, symbol) {
    reason = NULL
    if (any(constituent > "899999")) {
        reason = "B_share_detected"
    }
    if (any(constituent > "819999")) {
        reason = "NEEQ_share_detectd"
    }
    if (any(grep('HK', constituent))) {
        reason = "HK_share_detected"
    }
    if (any(c(startsWith(constituent, "4"), startsWith(constituent, "8")))) {
        reason = "BSE_share_detected"
    }
    if (!is.null(reason)) {
        InfoLog(componentName, "{reason} in {symbol}, skipped.")
    }
    return(!is.null(reason))
}


#' Get the close price of the symbol on the date. Index symbol should add Idx prefix when check data source bar/60 data.
#' And remove the prefix when crawl the index close price if failed to get from data source.
#' @export
GetSymbolClosePxFromDs = function(dataSource, symbol, date) {
    date = MakeTime(date)
    close = dataSource$ReadData("ChinaStock", "Bar/60", symbol, date, date + days(1))$close
    if (is.null(close)) {
        return(NULL)
    } else {
        return(round(last(close), 2))
    }
}


#' nodoc
#' @export
GetSymbolClosePxByCrawl = function(symbol, date, rawDataRoot) {
    return(GetIndexClosePrice(symbol, as.character(date), rawDataRoot))
}


#' nodoc
#' @export
CalculateQtyWeight = function(indicesToCalc, rawDataRoot, caledFile, ds) {
    if (IsEmpty(indicesToCalc)) {
        return(TRUE)
    }

    mkt = "ChinaStock"
    cuttingHour = Demeter::GetTradingDayCuttingHour(mkt)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(mkt)
    totalIndices = nrow(indicesToCalc)
    retryIndices = NULL
    specialConstituent = NULL
    calculatedIndices = NULL
    crawlCount = 0
    for(i in 1:totalIndices) {
        date = MakeTime(indicesToCalc$timestamp[i])
        timestamp = GetBeginOfNextTradingDay(date, ds, mkt)
        symbol = indicesToCalc$symbol[i]
        InfoLog("CalculateQtyWeight", "Calculating index {symbol} on {date}")

        rawData = fread(file.path(rawDataRoot, date, paste0(symbol, ".csv")))
        if (is.character(rawData[[6]])) {
            rawData[[6]] = sprintf("%06s", rawData[[6]])
        } else {
            rawData[[6]] = sprintf("%06d", rawData[[6]])
        }
        if (CheckCSIndexNeedSkipped(rawData[[6]], symbol)) {
            specialConstituent = c(specialConstituent, symbol)
            next
        }

        indexClosePx = GetSymbolClosePxFromDs(ds, symbol, date)
        if (is.null(indexClosePx)) {
            indexClosePx = GetSymbolClosePxByCrawl(symbol, date, rawDataRoot)
            Sys.sleep(runif(1, 1, 3))
            crawlCount = crawlCount + 1
            if (crawlCount %% 30 == 0) {
                Sys.sleep(runif(1, 7, 9) * 4)
            }
        }
        if (is.null(indexClosePx) || !is.numeric(indexClosePx)) {
            InfoLog("CalculateQtyWeight", ("Failed to get closePx for index {symbol}: {indexClosePx}. Add it to retry list."))
            retryIndices = c(retryIndices, symbol)
            next
        }

        # Due to filtered out indices with special shares (begin with HK) in the constituent,
        # next line is safe because mainland share codes are all numbers.
        data = data.table(
            timestamp = timestamp,
            symbol = symbol,
            constituent = rawData[[6]],
            valueWeight = as.numeric(rawData[[ncol(rawData)]]) / 100,
            closePx = sapply(rawData[[6]], \(s) GetSymbolClosePxFromDs(ds, s, date))
        )
        if (!is.numeric(data$closePx)) {
            isNumeric = sapply(data$closePx, is.numeric)
            idx = which(!isNumeric)
            symbols = data$constituent[idx]
            InfoLog("CalculateQtyWeight", "Failed to get index constituent closePx. Add it to special list. Failed constituent: {toString(symbols)}")
            specialConstituent = c(specialConstituent, symbol)
            next
        }
        data[, qty := round(valueWeight * indexClosePx / closePx, 8)]
        fwrite(data, caledFile, append = file.exists(caledFile))
        calculatedIndices = c(calculatedIndices, symbol)
        InfoLog("CalculateQtyWeight", "Index {symbol} on {date} calculated. Progress: {i}/{totalIndices}")
        next
    }
    return(list(
        retryIndices = retryIndices,
        specialConstituent = specialConstituent,
        calculatedIndices = calculatedIndices
    ))
}


#' nodoc
#' @export
SetDailyUpdateCSIndexState = function(state, notify, component, targetDate, reason = "") {
    if (state == "Failed") {
        ErrorLog(component, "Failed to update CSI: {reason}")
    } else {
        InfoLog(component, glue("ChinaStock Index daily update {state}"))
    }
    if (notify) {
        SetStateVar(component, state, targetDate)
        resultIsSuccess = switch(state, "Skipped" = TRUE, "Done" = TRUE, "Doing" = NULL, "Failed" = FALSE, NULL)
        reason = ifelse(state == "Failed", glue(": {reason}"), ".")
        WechatToDataWatcher(component, glue("ChinaStock Index daily update {state}{reason}"), resultIsSuccess = resultIsSuccess)
    }
    if (state != "Doing") {
        quit()
    }
}
