#' nodoc
#' @export
InitTradingSession = function(dataSource, time = NULL,
                              marketType = c("ChinaFuture", "ChinaOption", "UKFuture", "CMEFuture")) {
    lapply(marketType, function(mkt) {
        InfoLog("InitTradingSession", "Init {mkt} Trading Session")
        normalSession = dataSource$ReadData(mkt, "Misc/Session")
        tradingDay = dataSource$ReadData(mkt, "Misc/TradingDay")
        if (is.null(normalSession) || is.null(tradingDay)) {
            WarningLog("InitTradingSession", "No session data: {mkt}.")
            return(invisible())
        }
        session = GetSessionByTradingDay(mkt, normalSession, tradingDay)
        if (!is.null(session)) {
            if (!is.null(time)) session = session[timestamp <= MakeTime(time)]
            RcppInitTradingSession(mkt, session)
        }
    })
    return(invisible())
}

#' nodoc
#' @export
GetWeekday = function(time) {
    return(RcppGetWeekday(time))
}

#' nodoc
#' @export
GetSessionType = function(time, marketType, symbol) {
    return(RcppGetSessionType(time, marketType, symbol))
}

#' nodoc
#' @export
SessionTypeToStr = function(sessionType) {
    return(RcppConvertSessionTypeToString(sessionType))
}

#' nodoc
#' @export
StrToSessionType = function(str) {
    return(RcppConvertStringToSessionType(str))
}

#' Get edge of trading session
#' @param sessionEdge: could be begin, end, both
#' @param sessionType: filter the sessions by type
#' @export
GetSessionEdge = function(dataSource, marketType, symbol, beginTime, endTime, sessionEdge = "begin", sessionType = NULL) {
    tdSeq = GetTradingDaySeq(beginTime, endTime, dataSource, marketType) %>% .[-length(.)]
    selectColnames = c("type", sessionEdge)
    sessionData = rbindlist(GetSession(tdSeq, marketType, symbol))[begin <= endTime & end >= beginTime, ..selectColnames]
    if (!is.null(sessionType)) sessionData = sessionData[type %in% sessionType]
    return(sessionData)
}

#' nodoc
#' @export
GetSession = function(time, marketType, symbol) {
    AssertNotNull(time, marketType, symbol)
    Assert(!is.unsorted(time))
    ret = RcppGetSession(time, marketType, symbol)
    tz = GetTimezone(marketType)
    ret = lapply(ret, function(x) {
        if (IsEmpty(x)) {
            return(NULL)
        }
        setDT(x)
        setorder(x, begin)
        x$begin = DoubleToTime(x$begin, tz)
        x$end = DoubleToTime(x$end, tz)
        x
    })
    return(ret)
}

#' nodoc
#' @export
UnionSessions = function(sessionsLeft, sessionsRight, marketType) {
    ret = RcppUnionSessions(sessionsLeft, sessionsRight, marketType)
    setDT(ret)
    if (!IsEmpty(ret)) {
        ret[, begin := as.ITime(begin)]
        ret[, end := as.ITime(end)]
    }
    return(ret)
}

#' nodoc
#' @export
DeduceEveningSession = function(time, marketType) {
    ret = RcppDeduceEveningSession(time, marketType)
    setDT(ret)
    if (!IsEmpty(ret)) {
        ret[, begin := as.ITime(begin)]
        ret[, end := as.ITime(end)]
    }
    return(ret)
}
