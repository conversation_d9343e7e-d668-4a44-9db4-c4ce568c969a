GetChinaStockIndexOptionSession = function(tradingDay) {
    mkt = "ChinaOption"
    cuttingHour = GetTradingDayCuttingHour(mkt)
    tradeOnWeekends = GetTradeOnWeekends(mkt)
    tz = GetTimezone(mkt)
    ss = "09:30-11:30,13:00-15:00"

    ioListingDate = MakeTime("2019-12-23", tz)
    ioListingBegin = GetBeginOfTradingDayNoDS(ioListingDate, cuttingHour, tradeOnWeekends)
    ioSession = data.table(timestamp = ioListingBegin, derivname = "IO", tradinghours = "09:30-11:30,13:00-15:00")

    moListingDate = MakeTime("2022-07-22", tz)
    moListingBegin = GetBeginOfTradingDayNoDS(moListingDate, cuttingHour, tradeOnWeekends)
    moSession = data.table(timestamp = moListingBegin, derivname = "MO", tradinghours = "09:30-11:30,13:00-15:00")

    coPd = GetLoadedProducts(mkt)
    csioDerivname = coPd[IsChinaStockIndexOption(mkt, derivativeName), unique(derivativeName)]
    begin = GetBeginOfTradingDayNoDS(tradingDay, cuttingHour, tradeOnWeekends)
    session = data.table(timestamp = begin, derivname = csioDerivname, tradinghours = ss)

    ret = rbind(ioSession, moSession, session)

    return(ret)
}

GetStockIndexFutureSession = function(derivname, tradingDay, cuttingHour, tradeOnWeekends) {
    oldSs = "09:15-11:30,13:00-15:15"
    newSs = "09:30-11:30,13:00-15:00"
    tz = attr(tradingDay, "tzone")
    changeTime = MakeTime("2016-01-02", tz = tz)
    changeTime = GetBeginOfTradingDayNoDS(changeTime, cuttingHour, tradeOnWeekends)
    begin = GetBeginOfTradingDayNoDS(tradingDay, cuttingHour, tradeOnWeekends)

    session = data.table(
        timestamp = begin, derivname = derivname,
        tradinghours = ifelse(begin < changeTime, oldSs, newSs)
    )

    return(session)
}

GetTreasuryFutureSession = function(derivname, tradingDay, cuttingHour, tradeOnWeekends) {
    oldSs = "09:15-11:30,13:00-15:15"
    newSs = "09:30-11:30,13:00-15:15"
    tz = attr(tradingDay, "tzone")
    changeTime = MakeTime("2020-07-20", tz = tz)
    changeTime = GetBeginOfTradingDayNoDS(changeTime, cuttingHour, tradeOnWeekends)
    begin = GetBeginOfTradingDayNoDS(tradingDay, cuttingHour, tradeOnWeekends)

    session = data.table(
        timestamp = begin, derivname = derivname,
        tradinghours = ifelse(begin < changeTime, oldSs, newSs)
    )

    return(session)
}

GetCommodityFutureSession = function(marketType, dataSource, derivName, derivNameInfo, tradingDay) {
    cuttingHour = GetTradingDayCuttingHour(marketType)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)

    dayTradingHours = "09:00-10:15,10:30-11:30,13:30-15:00"
    session = data.table()

    derivNameBeginDay = GetTradingDayNoDS(min(derivNameInfo$begin), cuttingHour, cuttingMethod, tradeOnWeekends)
    derivNameTradingDay = tradingDay[tradingDay >= derivNameBeginDay]
    for (i in seq_along(derivNameTradingDay)) {
        b = GetBeginOfTradingDayNoDS(derivNameTradingDay[i], cuttingHour, tradeOnWeekends)
        e = GetBeginOfNextTradingDayNoDS(derivNameTradingDay[i], cuttingHour, tradeOnWeekends)

        unionEvenTradingHours = c()

        validDerivNameInfo = derivNameInfo[!(begin > e | end < b)]

        hasValidData = FALSE
        for (sym in validDerivNameInfo$symbol) {
            derivNameData = dataSource$ReadData(marketType, "Tick", sym, b, e)
            derivNameData = AdjustExchtime(derivNameData)
            derivNameData = FilterDataByClockskew(derivNameData)
            if (!IsEmpty(derivNameData)) hasValidData = TRUE
            evenTradingHours = DeduceEveningSession(derivNameData$exchtime, marketType)
            unionEvenTradingHours = UnionSessions(unionEvenTradingHours, evenTradingHours, marketType)
        }

        if (!hasValidData) next

        evenTradingHoursStr = ""
        if (!IsEmpty(unionEvenTradingHours)) {
            unionEvenTradingHours[, begin := strftime(begin, format = "%H:%M")]
            unionEvenTradingHours[, end := strftime(end, format = "%H:%M")]
            unionEvenTradingHours[end == "23:59", end := "00:00"]
            beforeCrossNight = which(unionEvenTradingHours$end == "00:00")
            afterCrossNight = which(unionEvenTradingHours$begin == "00:00")
            if (length(beforeCrossNight) > 0 && length(afterCrossNight) > 0) {
                unionEvenTradingHours$begin[afterCrossNight] = unionEvenTradingHours$begin[beforeCrossNight]
                unionEvenTradingHours = unionEvenTradingHours[-beforeCrossNight]
            }
            unionEvenTradingHours[, str := paste0(begin, "-", end)]
            evenTradingHoursStr = paste0(unionEvenTradingHours$str, seq = ",", collapse = "")
        }
        tradingHours = paste0(evenTradingHoursStr, dayTradingHours)
        session = rbind(session, list(timestamp = b, derivname = derivName, tradinghours = tradingHours))
    }
    return(session)
}

GetChinaFutureSessionsFromData = function(dataSource, begin, dataType = "Tick") {
    marketType = "ChinaFuture"
    cuttingHour = GetTradingDayCuttingHour(marketType)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    tz = GetTimezone(marketType)

    beginDate = GetTradingDayNoDS(begin, cuttingHour, cuttingMethod, tradeOnWeekends)
    endDate = GetTradingDayNoDS(GetNow(), cuttingHour, cuttingMethod, tradeOnWeekends)

    tradingDay = dataSource$ReadData(marketType, "Misc/TradingDay")
    tradingDay = tradingDay[tradingday >= beginDate & tradingday <= endDate, tradingday]

    srcInfo = dataSource$ListData(marketType, dataType)
    srcInfo[, derivname := GetDerivNameOfSymbol(marketType, symbol)]
    derivNameInfoList = split(srcInfo, by = c("derivname"))

    allSessions = data.table()

    for (derivName in names(derivNameInfoList)) {
        InfoLog("GetChinaFutureSessionsFromData", "Deducing sessions: ", derivName)

        if (IsChinaStockIndexFuture(marketType, derivName)) {
            session = GetStockIndexFutureSession(derivName, tradingDay, cuttingHour, tradeOnWeekends)
        } else if (IsChinaTreasuryFuture(marketType, derivName)) {
            session = GetTreasuryFutureSession(derivName, tradingDay, cuttingHour, tradeOnWeekends)
        } else if (IsChinaCommodityFuture(marketType, derivName)) {
            session = GetCommodityFutureSession(
                marketType, dataSource, derivName,
                derivNameInfoList[[derivName]], tradingDay
            )
        } else {
            warnings("Cannot find sessions for deriv name: ", derivName)
            next
        }
        allSessions = rbind(allSessions, session)
    }

    if (!IsEmpty(allSessions)) {
        allSessions = allSessions[timestamp >= begin]
        setattr(allSessions$timestamp, "tzone", tz)
        setattr(allSessions, "marketType", marketType)
        setattr(allSessions, "dataType", "Misc/Session")
        setorder(allSessions, timestamp, derivname)
    }

    return(allSessions)
}

#' nodoc
#' @export
GenChinaFutureSessionByData = function(src, dest, dataType = "Tick", lookbackDays = NULL, blackList = NULL) {
    InfoLog("GenChinaFutureSessionByData", "Updating session by ChinaFuture/{dataType} {src$GetDesc()} => {dest$GetDesc()}")

    marketType = "ChinaFuture"
    tz = GetTimezone(marketType)
    listInfo = dest$ListData(marketType, "Misc/Session")
    if (is.null(listInfo)) {
        begin = MakeTime("2015-01-01", tz)
        WarningLog("GenChinaFutureSessionByData", "Update Session from 2015-01-01, may cause a lot of time")
    } else {
        begin = max(listInfo$begin)
        if (!is.null(lookbackDays)) {
            cuttingHour = GetTradingDayCuttingHour(marketType)
            tradeOnWeekends = GetTradeOnWeekends(marketType)
            btd = GetBeginOfTradingDayNoDS(GetToday(), cuttingHour, tradeOnWeekends)
            lookbackTime = AddTradingDays(btd, -lookbackDays, src, "ChinaFuture")
            begin = max(begin, lookbackTime)
        }
    }

    allSessions = GetChinaFutureSessionsFromData(src, begin, dataType)

    if (!IsEmpty(allSessions)) {
        if (!is.null(blackList)) {
            allSessions = allSessions[!derivname %in% blackList]
        }
        dest$WriteData(allSessions)
        InfoLog("GenChinaFutureSessionByData", "Success update session from begin time: {TimeToStr(begin)}")
    } else {
        InfoLog("GenChinaFutureSessionByData", "Failed update session from begin time: {TimeToStr(begin)}")
    }
}

RemoveEveningSessionOnHoliday = function(tradingHours) {
    sapply(str_split(tradingHours, ","), \(x) {
        x = x[as.numeric(str_sub(x, end = 2)) < 17]
        return(paste0(x, collapse = ","))
    })
}

#' Get session by normal session and trading day, because right before public
#' holiday, the evening session is cancelled
#' @export
GetSessionByTradingDay = function(marketType, normalSession, tradingDay) {
    if (!marketType %in% c("ChinaFuture", "ChinaOption")) {
        return(normalSession)
    }

    tradingDay = copy(tradingDay)
    tradingDay[, preDay := c(1, head(tradingDay$tradingday, -1))]
    tradingDay[, isMonday := wday(tradingday) == 2]
    tradingDay[, isAfterHoliday := ifelse(isMonday, preDay != as.numeric(tradingday - days(3)), preDay != as.numeric(tradingday - days(1)))]

    uDerivname = unique(normalSession$derivname)
    allTradingDaySession = rbindlist(lapply(uDerivname, \(deriv) {
        ss = normalSession[derivname == deriv]

        updatedTradingDay = tradingDay
        if (last(ss$tradinghours) == "") {
            updatedTradingDay = updatedTradingDay[tradingDay$timestamp < last(ss$timestamp)]
        }

        index = findInterval(updatedTradingDay$timestamp, ss$timestamp)
        validIdx = index > 0
        index = index[validIdx]
        tradingDaySession = ss[index]
        tradingDaySession[, timestamp := updatedTradingDay$timestamp[validIdx]]
        tradingDaySession[, isAfterHoliday := updatedTradingDay$isAfterHoliday[validIdx]]
    }))

    allTradingDaySession[isAfterHoliday == TRUE, tradinghours := RemoveEveningSessionOnHoliday(tradinghours)]
    allTradingDaySession[, isAfterHoliday := NULL]
    setorder(allTradingDaySession, timestamp, derivname)
    RecoverAttrFromData(allTradingDaySession, normalSession)

    newSession = RemoveAdjacentDuplicateData(allTradingDaySession)
    return(newSession)
}

#' nodoc
#' @export
GetTradingDayByCheckingSolidFile = function(solidFilePath, marketType, dataType, destSource, minFileSize = 10) {
    InfoLog("GetTradingDayByCheckingSolidFile", "Start update trading day")

    fileMinSizeInBytes = minFileSize * 1024 * 1024

    tz = GetTimezone(marketType)
    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)

    listInfo = destSource$ReadData(marketType, "Misc/TradingDay")
    if (is.null(listInfo)) {
        begin = MakeTime("2015-01-01", tz)
    } else {
        begin = last(listInfo$tradingday)
    }

    path = file.path(solidFilePath, marketType, dataType)
    tradingDay = c()
    for (file in list.files(path)) {
        mySrcFile = file.path(path, file)
        if (file.size(mySrcFile) >= fileMinSizeInBytes) {
            time = MakeTime(sub("\\..*", "", file), tz = tz)
            tradingDay = c(tradingDay, time)
        }
    }

    validDays = tradingDay[which(tradingDay > begin)]

    if (length(validDays) > 0) {
        validDays = MakeTime(validDays, tz = tz)
        begin = GetBeginOfTradingDayNoDS(validDays, cuttingHour, tradeOnWeekends)
        result = data.table(timestamp = begin, tradingday = validDays)
        setattr(result, "marketType", marketType)
        setattr(result, "dataType", "Misc/TradingDay")
        setattr(result, "symbol", NULL)
        destSource$WriteData(result)
        InfoLog("GetTradingDayByCheckingSolidFile", "Success update new trading days, count: {length(validDays)}, latest trading day: {TimeToStr(last(validDays)}")
        return(result)
    }

    InfoLog("GetTradingDayByCheckingSolidFile", "Failed update trading day: no trading day greater than {TimeToStr(begin)}")
    return(NULL)
}