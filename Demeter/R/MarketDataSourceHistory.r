#' nodoc
#' @export
GetMDSHFilePath = function(marketType) {
    filename = glue("MarketDataSourceHistory_{marketType}.csv")
    return(system.file("Data", filename, package = "Demeter"))
}

#' nodoc
#' @export
GetMDSH = function(path) {
    if (!file.exists(path)) {
        return(NULL)
    }
    mdsh = fread(file.path(path), colClasses = c("begin" = "character"))
    if (IsEmpty(mdsh)) {
        return(NULL)
    }

    # add column end
    mdsh[, index := .I]
    mdsh = mdsh[order(dataType, dsRoot, begin)]
    mdsh[, end := shift(begin, type = "lead"), by = .(dsRoot, dataType)]
    mdsh[is.na(end), end := DateToStr(GetToday())]
    mdsh = mdsh[order(index)]

    # split srcdsRoot and dsRoot
    mdsh$srcdsRoot = lapply(mdsh$srcdsRoot, SplitCommaValue)
    mdsh$dsRoot = lapply(mdsh$dsRoot, SplitCommaValue)
    return(mdsh)
}

#' nodoc
#' @export
MDSHCheckSolidDataAgainstYesterday = function(mdsh, marketType, dataType, targetDate = GetToday(GetTimezone(marketType))) {
    fstItem = first(mdsh[marketType == marketType & fileFormat == "fst"])
    if (IsEmpty(fstItem)) {
        FatalLog("MDSHCheckSolidDataAgainstYesterday", "No fst item found in mdsh of {marketType}")
    }
    ds = MDSHGetItemDataSource(fstItem)$dstDs

    for (dt in dataType) {
        checkItem = last(mdsh[marketType == marketType & srcDataType == dt & fileFormat == "csv"])
        path = first(checkItem$srcdsRoot[[1]])
        Assert(CheckSolidDataSizeAgainstYesterday(path, marketType, dt, ds, checkTime = targetDate))
    }
    return(TRUE)
}


#' nodoc
#' @export
MDSHUpdateTask = function(marketType, dataTypes, stateVar, targetDate, checkFileSize = TRUE, fullScan = FALSE) {
    SetStateVar(stateVar, "Doing", targetDate)
    mdsh = GetMDSH(GetMDSHFilePath(marketType))
    mdsh[, fullScan := fullScan]
    if (checkFileSize) {
        for (dataType in dataTypes) {
            Assert(MDSHCheckSolidDataAgainstYesterday(mdsh, marketType, glue("Solid{dataType}"), MakeTime(targetDate, GetTimezone(marketType))))
        }
    } else {
        InfoLog(componentName, "Skip checking file size")
    }
    MDSHUpdateData(mdsh, targetDate)
    SetStateVar(stateVar, "Done", targetDate)
}


#' nodoc
#' @export
MDSHUpdateData = function(mdsh, targetDate = DateToStr(GetToday())) {
    for (i in 1:nrow(mdsh)) {
        item = mdsh[i]
        item = MDSHParseItem(item)
        item$targetDate = targetDate
        if (item$updateMethod == "Manually") next
        updateMethod = paste0("MDSHMethod", item$updateMethod)
        hasSignal = ifelse(is.na(item$signal), FALSE, item$signal != "")
        if (hasSignal) {
            SetStateVar(item$signal, "Doing", targetDate)
        }
        InfoLog("MDSHUpdateData", "#{i} Updating mdsh item by Method {updateMethod}: {item$srcdsRoot}: {item$srcMarketType}/{item$srcDataType}[{item$srcFileFormat}] => {item$dsRoot}/{item$marketType}/{item$dataType}[{item$fileFormat}]")
        if (!is.null(updateFunc <- get0(updateMethod))) {
            updateFunc(item)
            MDSHUpdateItemCache(item)
            state = "Done"
        } else {
            state = "Failed"
            ErrorLog("MDSHUpdateData", "Failed to find mdsh update method: {updateMethod}")
        }
        if (hasSignal) {
            SetStateVar(item$signal, state, targetDate)
        }
        gc()
    }
}

#' nodoc
#' @export
MDSHParseItem = function(item) {
    item = apply(item, 1, as.list)[[1]]
    if (is.na(item$arguments) || IsEmpty(item$arguments) || item$arguments == "") {
        return(item)
    }
    arguments = strsplit(item$arguments, ",")
    name = sapply(arguments[[1]], \(x) strsplit(x, "=")[[1]][1])
    value = lapply(arguments[[1]], \(x) eval(parse(text = x)))
    names(value) = name
    item$arguments = NULL
    item = append(item, value)
    return(item)
}

MDSHUpdateItemCache = function(item) {
    marketType = item$marketType
    dataType = item$dataType
    fileFormat = item$fileFormat
    if (fileFormat == "fst") {
        itemDs = MDSHGetItemDataSource(item)$dstDs
        itemDs$ListData(marketType, dataType)
    }
}

#' nodoc
#' @export
MDSHGetItemDataSource = function(item) {
    fileFormat = item$fileFormat
    srcFileFormat = item$srcFileFormat
    srcDataPath = first(item$srcdsRoot)
    dstDataPath = first(item$dsRoot)
    dstDs = MDSHGetDataSource(dstDataPath, fileFormat)
    srcDs = MDSHGetDataSource(srcDataPath, srcFileFormat)
    return(list(dstDs = dstDs, srcDs = srcDs))
}

MDSHGetDataSource = function(root, fileFormat) {
    if (is.na(root) || is.null(root)) {
        return(NULL)
    } else if (root == "rq") {
        GetRQDataSource()
    } else if (identical(fileFormat, "csv")) {
        GetCSVDataSource(root)
    } else if (identical(fileFormat, "fst")) {
        GetFSTDataSource(root)
    } else if (root == "ak") {
        GetAKDataSource()
    } else {
        return(NULL)
    }
}

#' nodoc
#' @export
MDSHMethodCopyData = function(item) {
    marketType = item$marketType
    dataType = item$dataType
    fileFormat = item$fileFormat
    srcMarketType = item$srcMarketType
    srcDataType = item$srcDataType
    itemDs = MDSHGetItemDataSource(item)
    dstDs = itemDs$dstDs
    srcDs = itemDs$srcDs

    if (dataType == srcDataType) {
        if (srcMarketType == marketType) {
            CopyData(marketType, dataType, srcDs, dstDs, end = MDSHGetItemCopyEnd(item))
        } else {
            CopyMiscDataCrossMarketType(srcMarketType, marketType, dataType, srcDs, dstDs)
        }
    }
}

MDSHGetItemCopyEnd = function(item) {
    marketType = item$marketType
    srcDataPath = first(item$srcdsRoot)
    dataType = item$dataType

    if (dataType == "Misc/PxLimit" && srcDataPath == "rq") {
        targetDate = MakeTime(min(item$targetDate, item$end), GetTimezone(item$marketType))
        cuttingHour = GetTradingDayCuttingHour(marketType)
        cuttingMethod = GetTradingDayCuttingMethod(marketType)
        tradeOnWeekends = GetTradeOnWeekends(marketType)
        rqPxlimitCopyEnd = GetBeginOfTradingDayNoDS(targetDate, cuttingHour, tradeOnWeekends)
        rqPxlimitCopyEnd = AddTradingDaysNoDS(rqPxlimitCopyEnd, -1, tradeOnWeekends)
        return(rqPxlimitCopyEnd)
    }

    return(NULL)
}

#' nodoc
#' @export
MDSHMethodRsyncData = function(item) {
    standardCSVPath = item$dsRoot[[1]]
    marketType = item$marketType
    dataType = item$dataType
    srcDataType = item$srcDataType
    sourcePath = item$srcdsRoot[[1]]

    destDataPath = file.path(standardCSVPath, marketType, dataType)
    date = last(list.dirs(destDataPath, full.names = FALSE, recursive = FALSE))
    if (length(date) == 0) {
        date = first(item$begin)
    }

    while (date <= min(item$targetDate, item$end)) {
        srcDataPath = file.path(sourcePath, marketType, srcDataType, date)
        if (any(file.exists(srcDataPath))) {
            InfoLog("MDSHMethodRsyncData", "Updating {marketType}/{dataType}: {toString(srcDataPath)} => {destDataPath}")
            for (path in srcDataPath) {
                BashExec(glue("rsync -aiz {path} {destDataPath}"))
            }
        }
        date = DateToStr(MakeTime(date) + days(1))
    }
}


#' nodoc
#' @export
MDSHMethodDivideData = function(item) {
    standardCSVPath = item$dsRoot
    marketType = item$marketType
    dataType = item$dataType
    srcDataType = item$srcDataType
    sourcePath = item$srcdsRoot
    badDataPath = GetFirstOK(item$badDataPath, "~/RawData/DailyUpdateLog")
    checkTimestampOrder = GetFirstOK(item$checkTimestampOrder, TRUE)
    stopOnDataError = GetFirstOK(item$stopOnDataError, TRUE)
    destDataPath = file.path(standardCSVPath, marketType, dataType)

    date = last(list.dirs(destDataPath, full.names = FALSE, recursive = FALSE))
    if (length(date) == 0) {
        date = first(item$begin)
    }

    while (date <= min(item$targetDate, item$end)) {
        srcDataPath = file.path(sourcePath, marketType, srcDataType, paste0(date, ".csv"))
        if (any(file.exists(srcDataPath))) {
            InfoLog("MDSHMethodDivideData", "Updating {marketType}/{dataType}: {toString(srcDataPath)} => {destDataPath}")
            DivideSolidData(sourcePath, standardCSVPath, badDataPath, marketType, dataType, checkTimestampOrder = checkTimestampOrder, divideDate = date, stopOnDataError = stopOnDataError)
        }

        date = DateToStr(MakeTime(date) + days(1))
    }
}

#' nodoc
#' @export
MDSHMethodUpdateMiscDataFromSolid = function(item) {
    solidFileRoot = item$srcdsRoot
    marketType = item$marketType
    dataType = item$dataType
    itemDs = MDSHGetItemDataSource(item)
    dstDs = itemDs$dstDs
    GetMiscDataFromSolidFile(solidFileRoot, marketType, dataType, dstDs)
}

#' nodoc
#' @export
MDSHMethodUpdateSettlement = function(item) {
    dataType = item$dataType
    itemDs = MDSHGetItemDataSource(item)
    dstDs = itemDs$dstDs
    exchRawDataPath = item$srcdsRoot[[1]]
    CopySettlementData(exchRawDataPath, dataType, dstDs)
}

#' nodoc
#' @export
MDSHMethodUpdateTradingDayFromSolid = function(item) {
    solidFileRoot = item$srcdsRoot[[1]]
    marketType = item$marketType
    srcDataType = item$srcDataType
    itemDs = MDSHGetItemDataSource(item)
    dstDs = itemDs$dstDs
    GetTradingDayByCheckingSolidFile(solidFileRoot, marketType, srcDataType, dstDs)
}


#' nodoc
#' @export
MDSHMethodUpdateSession = function(item) {
    MDSHMethodCopyData(item)
    itemDs = MDSHGetItemDataSource(item)
    dstDs = itemDs$dstDs
    marketType = item$marketType
    InitTradingSession(dstDs, marketType = marketType)
}

#' nodoc
#' @export
MDSHMethodUpdateBar = function(item) {
    marketType = item$marketType
    dataType = item$dataType
    srcDataType = item$srcDataType
    itemDs = MDSHGetItemDataSource(item)
    dstDs = itemDs$dstDs
    srcDs = itemDs$srcDs
    barSize = sub("Bar/", "", dataType)
    ConvertDataToBar(marketType, srcDataType, srcDs, dstDs, barSize = barSize)
}

#' nodoc
#' @export
MDSHMethodUpdateMajor = function(item) {
    marketType = item$marketType
    dataType = item$dataType
    itemDs = MDSHGetItemDataSource(item)
    srcDs = itemDs$srcDs
    dstDs = itemDs$dstDs
    by = ifelse(dataType == "Misc/OIMajor", "oi", "vol")
    ConvertBarToFutureMajor(marketType, srcDs, dstDs, by = by)
}

#' nodoc
#' @export
MDSHMethodCrawlRawSettlement = function(item) {
    exchRawDataPath = item$dsRoot
    marketType = item$marketType
    ds = GetDefaultFSTDataSource()
    CrawlSettlementData(exchRawDataPath, marketType, ds)
}

#' nodoc
#' @export
MDSHMethodUpdateFundamental = function(item) {
    marketType = item$marketType
    itemDs = MDSHGetItemDataSource(item)
    srcDs = itemDs$srcDs
    dstDs = itemDs$dstDs
    info = srcDs$ListData(marketType, "Fundamental")
    for (symbol in info$symbol) {
        dataType = paste0("Fundamental/", symbol)
        CopyData(marketType, dataType, srcDs, dstDs)
    }
}

#' nodoc
#' @export
MDSHMethodArchiveCSVData = function(item) {
    marketType = item$marketType
    dataType = item$dataType
    archivedataPath = item$dsRoot
    srcDataPath = item$srcdsRoot
    fullScan = GetFirstOK(item$fullScan, FALSE)
    compression = item$fileFormat

    Assert(length(srcDataPath) == length(archivedataPath))

    for (i in seq_along(srcDataPath)) {
        ArchiveData(
            srcDataPath[i],
            archivedataPath[i],
            marketType,
            dataType,
            compression = compression, fullScan = fullScan
        )
    }
}

#' nodoc
#' @export
MDSHMethodUpdateETFConstituent = function(item) {
    itemDs = MDSHGetItemDataSource(item)
    # itemDs$dstDs$WriteData(GetLatestETFConstituent())
}

MDSHMethodArchiveFSTData = function(item) {
    marketType = item$marketType
    dataType = item$dataType
    archivedataPath = item$dsRoot
    srcDataPath = item$srcdsRoot
    srcDataType = item$srcDataType
    fullScan = GetFirstOK(item$fullScan, FALSE)
    compression = item$fileFormat
    tempDataPath = tempdir()
    tempDs = GetCSVDataSource(tempDataPath)
    srcDs = MDSHGetItemDataSource(item)$srcDs

    cuttingHour = GetTradingDayCuttingHour(marketType)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)

    begin = NULL
    latestArchivedTime = GetLatestArchivedFileTime(archivedataPath, marketType, dataType, compression)
    if (!is.null(latestArchivedTime)) {
        begin = GetBeginOfTradingMonthNoDS(latestArchivedTime, cuttingHour, cuttingMethod, tradeOnWeekends)
    }

    if (dataType == "Misc") {
        for (dt in srcDs$ListData(marketType, dataType)) {
            CopyData(marketType, dt, srcDs, tempDs)
        }
    } else if (dataType == "Fundamental") {
        for (fund in srcDs$ListData(marketType, dataType)$symbol) {
            fundDataType = paste0("Fundamental/", fund)
            CopyData(marketType, fundDataType, srcDs, tempDs, begin = begin)
        }
    } else if (dataType %in% c("Bar/60", "HtWebTick", "IndexConstituent")) {
        CopyData(marketType, srcDataType, srcDs, tempDs, begin = begin)
    } else {
        NotImplemented()
    }

    ArchiveData(
        tempDataPath,
        archivedataPath,
        marketType,
        dataType,
        compression = compression, fullScan = fullScan
    )
}

#' nodoc
#' @export
RestoreMDSHData = function(mdsh, marketType, compression = "xz", begin = NULL) {
    mdsh = mdsh[marketType == marketType & fileFormat == "xz" & srcFileFormat == "csv"]

    restoreDT = data.table()

    for (i in 1:nrow(mdsh)) {
        item = mdsh[i]
        archivedDataPath = item$dsRoot[[1]]
        restoreDataPath = item$srcdsRoot[[1]]
        srcDataType = item$srcDataType
        srcFileFormat = item$srcFileFormat
        Assert(length(archivedDataPath) == length(restoreDataPath))
        archiveDT = rbind(archiveDT, data.table(restoreDataPath, srcDataType, srcFileFormat))
    }

    restoreDT = unique(restoreDT)
    for (i in 1:nrow(restoreDT)) {
        RestoreFromArchive(
            restoreDT[i]$archivedDataPath,
            restoreDT[i]$restoreDataPath,
            marketType,
            restoreDT[i]$srcDataType,
            compression = compression, begin = begin
        )
    }
}