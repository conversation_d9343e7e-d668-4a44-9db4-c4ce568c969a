#' nodoc
#' @export
AdjustExchtime = function(data) {
    if (IsEmpty(data)) return(NULL)

    dataType = GetDataType(data)

    # long time ago Apollo doesn't save exchtime when writing following data types.
    # other data types are not affected
    if (is.null(dataType) || !dataType %in% c("Tick", "Best", "Deep", "MatchPriceQty", "TenEntrust", "OrderStatistic",
                                              "Orderbook5", "Orderbook10", "RemdIndexData", "RemdOptionOrderbook5",
                                              "RemdOrderAction", "RemdOrderActionSH", "RemdSipOrderbook10", "RemdOrderFill",
                                              "RemdOrderQueue", "RemdSpotTick"))
        return(data)

    hasExchtime = "exchtime" %in% colnames(data)
    if (hasExchtime && !any(is.na(data$exchtime))) return(data)

    adjData = copy(data)
    if (hasExchtime)
        adjData[is.na(exchtime), exchtime := timestamp]
    else
        adjData[, exchtime := timestamp]
    return(adjData)

}

#' nodoc
#' @export
FixCZCEExchtime = function(data) {
    if (IsEmpty(data)) return(data)

    AssertNotNull(data$exchtime)
    Assert(!any(is.na(data$exchtime)))

    marketType = attr(data, "marketType")
    dataType = attr(data, "dataType")
    symbol = attr(data, "symbol")
    exchange = FindExchange(marketType, symbol)

    if (is.na(exchange)) {
        WarningLog("FixCZCEExchtime", "Failed to find exchange of symbol: {symbol}")
        return(data)
    }

    if (!marketType %in% c("ChinaFuture", "ChinaOption") ||
        dataType != "Tick" ||
        exchange != "CZCE") return(data)

    cuttingHour = GetTradingDayCuttingHour(marketType)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    tz = GetTimezone(marketType)

    data[, tradingDay := GetTradingDayNoDS(exchtime, cuttingHour, cuttingMethod, tradeOnWeekends)]

    fixBegin = GetTradingDayNoDS(MakeTime("2019-10-15", tz), cuttingHour, cuttingMethod, tradeOnWeekends)

    data[tradingDay >= fixBegin, exchtime := DoubleToTime(FixCZCETime(exchtime), tz)]

    data[, tradingDay := NULL]
    return(data)
}

#' nodoc
#' @export
FixDataOneSide = function(data) {
    if (IsEmpty(data)) return(data)

    AssertNotNull(data$exchtime)
    Assert(!any(is.na(data$exchtime)))

    marketType = attr(data, "marketType")
    symbol = attr(data, "symbol")
    exchange = FindExchange(marketType, symbol)

    if (is.na(exchange)) {
        WarningLog("FixDataOneSide", "Failed to find exchange of symbol: {symbol}")
        return(data)
    }

    # ChinaOption and CFFEX Exchange has one side value, so not need to be fixed
    if (marketType != "ChinaFuture" ||
        !exchange %in% c("SHFE", "INE", "CZCE", "DCE")) return(data)

    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    tz = GetTimezone(marketType)
    # the raw data before 2020-01-01 needs to be fixed
    fixEnd = GetBeginOfTradingDayNoDS(MakeTime("2020-01-01", tz), cuttingHour, tradeOnWeekends)

    # the old raw data before 2019-10-15 does not have an exchtime column, we should use timestamp to compare
    data[timestamp < fixEnd, ':='(totalvol = 0.5 * totalvol, totalvalue = 0.5 * totalvalue, openinterest = 0.5 * openinterest)]
    data[]
    return(data)
}

#' nodoc
#' @export
FilterDataByTradingSession = function(dataSource, data) {
    if (IsEmpty(data)) return(data)

    AssertNotNull(data$exchtime)
    Assert(!any(is.na(data$exchtime)))

    marketType = GetMarketType(data)
    dataType = GetDataType(data)
    symbol = GetSymbol(data)

    td = dataSource$ReadData(marketType, "Misc/TradingDay")$tradingday
    cuttingHour = GetTradingDayCuttingHour(marketType)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)

    tradingDay = GetTradingDayNoDS(data$exchtime, cuttingHour, cuttingMethod, tradeOnWeekends)
    data = data[which(tradingDay %in% td)]

    tradingDay = unique(tradingDay)
    sessions = unique(rbindlist(GetSession(tradingDay, marketType, symbol)))

    if (is.unsorted(data$exchtime)) {
        unsortedIdx = which(diff(data$exchtime) < 0) + 1
        unsortedTime = data$exchtime[unsortedIdx]
        WarningLog("FilterDataByTradingSession", "Remove unsorted exchtime in {marketType}/{dataType}/{symbol}: {unsortedTime}")
        data = data[-unsortedIdx]
    }

    return(data[GetInSessionData(exchtime, sessions$begin, sessions$end)])
}


#' nodoc
#' @export
FilterDataByClockskew = function(data, clockskew = 60) {
    if (IsEmpty(data)) return(data)
    AssertNotNull(data$exchtime)
    Assert(!any(is.na(data$exchtime)))
    return(data[abs(difftime(timestamp, exchtime, units = "secs")) < clockskew])
}

SqueezeDataToSessionEnd = function(dataType, data, sessionEnd) {
    # remove reapeated value
    if (dataType == "OESOrderbook10" || dataType == "OESOrderQueue") {
        if (dataType == "OESOrderbook10")
            compareCols = colnames(data)[-c(1:3)]
        else
            compareCols = colnames(data)[-c(1:2)]

        uniqIndex = c()
        for (i in 1:(nrow(data))) {
            if (i == nrow(data) || anyDuplicated(data[i:(i + 1)], by = compareCols) == 0) uniqIndex = c(uniqIndex, i)
        }
        data = data[uniqIndex]
    }

    # squeeze exchtime to inside session
    for (i in 1:nrow(data)) {
        data[i, exchtime := sessionEnd - milliseconds() * (nrow(data) - i)]
    }
    return(data)
}

#' nodoc
#' @export
FilterOESData = function(dataSource, data) {
    if (IsEmpty(data)) return(data)

    AssertNotNull(data$exchtime)
    Assert(!any(is.na(data$exchtime)))

    marketType = attr(data, "marketType")
    dataType = attr(data, "dataType")
    symbol = attr(data, "symbol")

    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    cuttingMethod = Demeter::GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)

    # remove non-trading day by Misc/TradingDay
    data[, date := GetTradingDayNoDS(exchtime, cuttingHour, cuttingMethod, tradeOnWeekends)]
    td = dataSource$ReadData(marketType, "Misc/TradingDay")$tradingday
    validTd = unique(data$date)[unique(data$date) %in% td]
    data = data[date %in% validTd]

    # remove data before market open
    data[, sessionType := GetSessionType(exchtime, marketType, symbol)]

    if (any(is.na(data$sessionType))) {
        WarningLog("FilterOESData", "NA sessionType found in {marketType}/{dataType}/{symbol}")
    }

    data = data[sessionType != -1 & !is.na(sessionType)]
    if (IsEmpty(data)) return(NULL)

    # change vol unit from hand to share if stock index symbol is not with prefix "Idx399"
    if (dataType == "OESIndexData" && !startsWith(symbol, "Idx399")) {
        data[, totalvol := 100 * totalvol]
    }

    tradingDay = unique(data$date)
    sessions = unique(rbindlist(GetSession(tradingDay, marketType, symbol)))
    totalRemovedIndex = c()

    for (i in seq_along(tradingDay)) {
        dayIndex = which(data$date == tradingDay[i])
        startIndex = first(dayIndex)
        sessionTypes = unique(data$sessionType[dayIndex])

        for (st in sessionTypes) {
            if (st < 0) {
                outIndex = which(st == data$sessionType[dayIndex])
                preSessionIndex = findInterval(data$exchtime[dayIndex][first(outIndex)], sessions$end)
                preSessionType = sessions$type[preSessionIndex]
                preSessionEndTime = sessions$end[preSessionIndex]
                preSessionEndIndex = which(data$exchtime[dayIndex] == preSessionEndTime)
                if (length(preSessionEndIndex) > 0) {
                    outIndex = c(preSessionEndIndex, outIndex)
                }

                globalIndex = outIndex + startIndex - 1
                squeezedData = SqueezeDataToSessionEnd(dataType, data[globalIndex], preSessionEndTime)

                # update myData
                data[globalIndex[1]:globalIndex[nrow(squeezedData)]] = squeezedData

                #remove extra data if has repeatd data
                if (nrow(squeezedData) < length(globalIndex)) {
                    removedIndex = c(globalIndex[nrow(squeezedData) + 1]:last(globalIndex))
                    totalRemovedIndex = c(totalRemovedIndex, removedIndex)
                }
            }
        }
    }

    if (length(totalRemovedIndex) > 0) data = data[-totalRemovedIndex]

    data[, c("sessionType", "date") := NULL]
    return(data)
}

#' nodoc
#' @export
FilterData = function(dataSource, rawData) {
    if (IsEmpty(rawData)) return(NULL)

    marketType = GetMarketType(rawData)
    dataType = GetDataType(rawData)

    data = AdjustExchtime(rawData)
    validDataType = c("Tick", "Best", "Deep", "MatchPriceQty", "TenEntrust", "OrderStatistic", "Orderbook5", "Orderbook10")

    if ((marketType == "ChinaFuture" || marketType == "ChinaOption") && (dataType %in% validDataType)) {
        data = FilterDataByClockskew(data)
        data = FilterDataByTradingSession(dataSource, data)
        data = FixCZCEExchtime(data)
        data = FixDataOneSide(data)
    } else if (startsWith(dataType, "OES")) {
        data = FilterOESData(dataSource, data)
    }
    return(data)
}