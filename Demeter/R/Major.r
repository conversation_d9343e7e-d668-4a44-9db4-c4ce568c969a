#==================================================================================================
# utilities

AdjDataByColnames = function(data, adj, colnames) {
    for (colname in colnames) {
        if (colname %in% names(data))
            data[, eval(colname) := get(eval(colname)) * adj]
    }
    return(data)
}

AdjTickFunc = function(data, adj) {
    AdjDataByColnames(data, adj, c("bidpx", "askpx", "lastpx"))
    AdjDataByColnames(data, 1 / adj, c("bidvol", "askvol", "totalvol", "openinterest"))
}

AdjBarFunc = function(data, adj) {
    AdjDataByColnames(data, adj, c("open", "high", "low", "close"))
    AdjDataByColnames(data, 1 / adj, c("vol", "openinterest"))
}

AdjOb5Func = function(data, adj) {
    index = 0:4
    AdjDataByColnames(data, adj, c(paste0("bidpx", index), paste0("askpx", index), "lastpx"))
    AdjDataByColnames(data, 1 / adj, c(paste0("bidvol", index), paste0("askvol", index), "totalvol", "openinterest"))
}

AdjOb10Func = function(data, adj) {
    index = 0:9
    AdjDataByColnames(data, adj, c(paste0("bidpx", index), paste0("askpx", index)))
    AdjDataByColnames(data, 1 / adj, c(paste0("bidvol", index), paste0("askvol", index)))
}

GetAdjDataFunc = function(dataType) {
    if (dataType == "Tick") return(AdjTickFunc)
    if (dataType == "Orderbook5") return(AdjOb5Func)
    if (dataType == "Orderbook10") return(AdjOb10Func)
    if (startsWith(dataType, "Bar/")) return(AdjBarFunc)
    NotImplemented()
}

CalcMajorPxAdj = function(timestamp, major, bar1d) {
    adj = c(1)
    if (length(major) > 1) {
        for (i in 2:length(major)) {
            adj = c(adj, CalcPxAdjByClose(timestamp[i], major[i - 1], major[i], bar1d))
        }
    }
    return(adj)
}

CalcPxAdjByClose = function(time, prevSymbol, newSymbol, bar1d) {
    prevClose = bar1d[closetime == time & symbol == prevSymbol, close]
    newClose = bar1d[closetime == time & symbol == newSymbol, close]
    if (length(prevClose) == 0 || length(newClose) == 0) return(1)
    return(prevClose / newClose)
}

GetMajorCalColume = function(by) {
    return(switch(by,
           vol = "vol",
           oi = "openinterest",
    ))
}



#==================================================================================================
# implementation


#' nodoc
#' @export
BatchReadBar = function(dataSource, marketType, symbol, begin = GetConfig(DefaultBegin),
    end = GetConfig(DefaultEnd), fromBarSize = 60, convertToBarSize = "1d") {

    product = unique(GetDerivNameOfSymbol(marketType, symbol))
    if (length(product) > 1)
        WarningLog("BatchReadBar", "Symbols belong to multiple products: {toString(symbol)}, products = {toString(product)}")

    bars = rbindlist(lapply(symbol, function(sym) {
        bar = dataSource$ReadData(marketType, paste0("Bar/", fromBarSize), sym, begin, end)
        if (IsEmpty(bar)) return(NULL)
        if (convertToBarSize != fromBarSize)
            bar = CompressBar(bar, convertToBarSize)
        bar[, symbol := sym]
    }))

    if (IsEmpty(bars)) return(NULL)

    setorder(bars, "closetime") # fuck you CZCE
    setattr(bars, "marketType", marketType)
    setattr(bars, "derivName", product)
    setattr(bars, "barSize", BarSizeToSecond(convertToBarSize))

    return(bars)
}

#' nodoc
#' @export
CalcMajor = function(bar1d, by = "vol", latestMajor = NULL) {
    if (IsEmpty(bar1d)) return(NULL)

    marketType = GetMarketType(bar1d)
    tz = GetTimezone(marketType)
    derivName = attributes(bar1d)$derivName
    iscffex = FindExchange(marketType, derivName) == "CFFEX"
    calCol = GetMajorCalColume(by)
    bar1d[, yearMonth := GetFutureYearMonth(symbol)]
    setorderv(bar1d, cols = c("closetime", calCol), order = c(1, -1))
    splitBar = split(bar1d, by = "closetime")

    if (!is.null(latestMajor)) {
        major0 = latestMajor$major0
        major = latestMajor$major
        major2 = latestMajor$major2
        major3 = latestMajor$major3
        day = c(latestMajor$timestamp)
    } else {
        major0 = splitBar[[1]][yearMonth < first(yearMonth)][1, symbol]
        major = c()
        major2 = c()
        major3 = c()
        day = c()
    }

    changeRatio = 1.1

    for (d in names(splitBar)) {

        data = splitBar[[d]]
        closeTime = MakeTime(d, tz)
        if (iscffex && !startsWith(derivName, "T")) {
            nearestYearMonth = which.min(data$yearMonth)
            data = rbind(data[nearestYearMonth], data[-nearestYearMonth])
        } else {
            data = data[yearMonth >= first(data$yearMonth)]
        }

        if (length(major) > 0) {
            lastYearMonth = GetFutureYearMonth(last(major))
            data = data[yearMonth >= lastYearMonth]

            majorEqual = identical(last(major), data$symbol[1])
            major2Equal = identical(last(major2), data$symbol[2])
            major3Equal = identical(last(major3), data$symbol[3])

            if (IsEmpty(data) || (majorEqual && major2Equal && major3Equal)) next

            if (majorEqual) {
                lastMajor2Val = data[symbol == last(major2), get(calCol)]
                lastMajor3Val = data[symbol == last(major3), get(calCol)]

                major2Ratio = ifelse(IsEmpty(lastMajor2Val) || lastMajor2Val == 0, changeRatio, data[2, get(calCol)] / lastMajor2Val)
                major3Ratio = ifelse(IsEmpty(lastMajor3Val) || lastMajor3Val == 0, changeRatio, data[3, get(calCol)] / lastMajor3Val)

                if ((major2Equal && major3Ratio < changeRatio) ||
                    (major3Equal && major2Ratio < changeRatio)) next

                if ((major2Equal && major3Ratio >= changeRatio) ||
                    (major3Equal && major2Ratio >= changeRatio)) {

                    targetMajor2 = data$symbol[2]
                    targetMajor3 = data$symbol[3]

                } else {
                    if (major2Ratio >= changeRatio) {
                        # major2 changed and update major3
                        targetMajor2 = data$symbol[2]
                        major3Change = identical(last(major3), data$symbol[2]) || major3Ratio >= changeRatio
                        targetMajor3 = ifelse(major3Change, data$symbol[3], last(major3))
                    } else {
                        # major2 not changed and maybe to update major3
                        targetMajor2 = last(major2)
                        major3Ratio = ifelse(IsEmpty(lastMajor3Val) || lastMajor3Val == 0, changeRatio, data[symbol != targetMajor2][2, get(calCol)] / lastMajor3Val)
                        if (major3Ratio < changeRatio) next
                        targetMajor3 = data[symbol != targetMajor2][2, symbol]
                    }
                }
                targetMajor0 = last(major0)
            } else {
                targetMajor0 = last(major)
                targetMajor2 = data$symbol[2]
                targetMajor3 = data$symbol[3]
            }

            major0 = c(major0, targetMajor0)
            major = c(major, data$symbol[1])
            major2 = c(major2, targetMajor2)
            major3 = c(major3, targetMajor3)

        } else {
            major = c(major, data$symbol[1])
            major2 = c(major2, data$symbol[2])
            major3 = c(major3, data$symbol[3])
        }

        if (length(day) == 0)
            day = MakeTime(d, tz = tz)
        else
            day = c(day, MakeTime(d, tz = tz))

    }

    adj = CalcMajorPxAdj(day, major, bar1d)
    adj2 = CalcMajorPxAdj(day, major2, bar1d)
    adj3 = CalcMajorPxAdj(day, major3, bar1d)

    if (!is.null(latestMajor)) {
        n = nrow(latestMajor)
        major0 = major0[-n]
        major = major[-n]
        major2 = major2[-n]
        major3 = major3[-n]
        adj = adj[-n]
        adj2 = adj2[-n]
        adj3 = adj3[-n]
        day = day[-n]
    }

    if (length(major) == 0) return(NULL)

    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    timestamp = GetBeginOfNextTradingDayNoDS(day, cuttingHour, tradeOnWeekends)

    result = data.table(timestamp = timestamp,
                        derivname = derivName,
                        major0 = major0,
                        major = major, adj = adj,
                        major2 = major2, adj2 = adj2,
                        major3 = major3, adj3 = adj3)
    return(result)
}

#' nodoc
#' @export
GetMajor = function(dataSource, marketType, dataType, derivName,
                     begin = GetConfig(DefaultBegin), end = GetConfig(DefaultEnd),
                     adjPx = TRUE, majorLevel = 1, majorType = "vol", filterData = TRUE, TransformFunc = NULL) {

    Assert(majorType %in% c("vol", "oi"))

    begin = MakeTime(begin)
    end = MakeTime(end)
    majorDataType = ifelse(majorType == "vol", "Misc/Major", "Misc/OIMajor")
    devMajor = dataSource$ReadData(marketType, majorDataType, derivName)
    if (IsEmpty(devMajor)) return(NULL)

    if (majorLevel == 1) {
        symbol = devMajor$major
        adj = devMajor$adj
    } else if (majorLevel == 2) {
        symbol = devMajor$major2
        adj = devMajor$adj2
    } else if (majorLevel == 3) {
        symbol = devMajor$major3
        adj = devMajor$adj3
    } else {
        NotImplemented()
    }

    seqIdx = NewSequentialIndex(devMajor$timestamp, marketType, symbol,
                                   adj = adj, dataType = dataType)

    majorData = GetSequentialIndex(seqIdx, dataSource, begin, end, adjPx,
                                   filterData, TransformFunc)
    if (IsEmpty(majorData)) {
        WarningLog("GetMajor", "{derivName}'s major contract has no market data {marketType}/{dataType} on {begin} - {end}")
        return(NULL)
    }

    setattr(majorData, "marketType", marketType)
    setattr(majorData, "dataType", dataType)
    setattr(majorData, "symbol", derivName)

    return(majorData)
}

#' nodoc
#' @export
GetMajorSymbol = function(dataSource, marketType, derivName, time = GetNow(), majorType = "vol", majorLevel = 1) {
    Assert(majorType %in% c("vol", "oi"))
    Assert(majorLevel %in% c(0, 1, 2, 3))

    majorDataType = ifelse(majorType == "vol", "Misc/Major", "Misc/OIMajor")
    devMajor = dataSource$ReadData(marketType, majorDataType, derivName, end = time)
    if (IsEmpty(devMajor)) return(NULL)

    major = switch(majorLevel + 1,
        devMajor$major0,
        devMajor$major,
        devMajor$major2,
        devMajor$major3,
        NULL
    )
    return(last(major))
}