ReadSettlementFile = function(file, exchange) {
    if (exchange == "cffex") {
        data = scan(file, what = "character", encoding = "UTF-8", sep = "\n", quiet = TRUE)
        # "\u671f\u6743" is unicode of option in Chinese
        invalidIndex = which(startsWith(data, "\u671f\u6743"))
        endIndex = ifelse(length(invalidIndex) > 0, invalidIndex - 1, length(data))
        data = paste0(data[1:endIndex], seq = "\n", collapse = "")
        data = fread(data, encoding = "UTF-8", header = FALSE, tz = "")
        data = data[-1]
    } else {
        data = fread(file, encoding = "UTF-8", tz = "")
    }
    return(data)
}

GetSettlementFee = function(rawDataRoot, dir, fileName) {
    file = file.path(rawDataRoot, dir, fileName)
    if (!file.exists(file)) {
        return(NULL)
    }

    marketType = "ChinaFuture"

    time = MakeTime(dir, GetTimezone(marketType))
    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    timestamp = GetBeginOfTradingDayNoDS(time, cuttingHour, tradeOnWeekends)
    exchange = file_path_sans_ext(fileName)

    data = ReadSettlementFile(file, exchange)
    if (exchange == "shfe") {
        result = GetSHFESettlementFee(data)
    } else if (exchange == "dce") {
        result = GetDCESettlementFee(data)
    } else if (exchange == "czce") {
        result = GetCZCESettlementFee(data)
    } else if (exchange == "cffex") {
        result = GetCFFEXSettlementFee(data)
    } else if (exchange == "gfex") {
        result = GetGFEXSettlementFee(data)
    }

    result = c(list(timestamp), result)
    names(result) = c(
        "timestamp",
        "symbol",
        "rate",
        "pershare",
        "closetodayrate",
        "closetodaypershare"
    )
    result = as.data.table(result)
    return(result)
}

# ShangHai future exchange settlement patameters
GetSHFESettlementFee = function(data) {
    symbol = as.character(data[["Contract Code"]])
    rate = data[["Transaction Fee(‰)"]] * 1e-3
    pershare = data[["Transaction Fee Amount(RMB/Contract)"]]
    dicountRate = data[["Discount Rate for Closing-out Today's Position(%)"]] * 0.01
    todayCloseRate = rate * dicountRate
    todayClosePershare = pershare * dicountRate
    return(list(symbol, rate, pershare, todayCloseRate, todayClosePershare))
}

# Zhengzhou Commodity exchange settlement patameters
GetCZCESettlementFee = function(data) {

    #######################################################################
    # 2022-11-07 update:
    # 说明：
    # (1) 结算价：元/吨
    # (2) 是否单边市（U-涨停 D-跌停 N-非单边市）
    # (3) 交割手续费：元/张
    # (4) 若手续费收取方式为绝对值，则交易手续费、日内平今仓交易手续费：元/手
    #     若手续费收取方式为比例值，则交易手续费、日内平今仓交易手续费：‱
    #######################################################################

    symbol = data[["合约代码"]]
    rate = 0
    tradeFee = as.numeric(data[["交易手续费"]])
    todayCloseFee = GetFirstOK(data[["平今仓手续费"]], data[["日内平今仓交易手续费"]])

    if (is.null(data[["手续费收取方式"]])) {
        rate = 0
        pershare = tradeFee
        todayCloseRate = 0
        if (!is.null(data[["平今手续费减半"]])) {
            todayClosePershare = ifelse(data[["平今手续费减半"]] == "Y", 0.5 * pershare, pershare)
        } else {
            todayClosePershare = as.numeric(todayCloseFee)
        }
    } else {
        isAbsolute = data[["手续费收取方式"]] == "绝对值"
        pershare = ifelse(isAbsolute, tradeFee, 0)
        rate = ifelse(isAbsolute, 0, tradeFee * 0.0001)
        todayClosePershare = ifelse(isAbsolute, todayCloseFee, 0)
        todayCloseRate = ifelse(isAbsolute, 0, todayCloseFee * 0.0001)
    }

    return(list(symbol, rate, pershare, todayCloseRate, todayClosePershare))
}

# Dalian Commodity exchange settlement patameters from English version file
GetDCESettlementFee = function(data) {
    if (any(data[["Transaction Fee Open"]] != data[["Transaction Fee Close"]]) || any(data[["Transaction Fee Intra-Day Open"]] != data[["Transaction Fee Intra-Day Close"]])) {
        FatalLog("GetDCESettlementFee", "Open fee are not all equal to close fee, unprocessed fee")
    }
    symbol = as.character(data[["Code"]])
    rate = ifelse(data[["Fee Collection Type"]] == "ratio value", data[["Transaction Fee Open"]], 0) * 1e-4
    pershare = ifelse(data[["Fee Collection Type"]] == "absolute", data[["Transaction Fee Open"]], 0)
    todayCloseRate = ifelse(data[["Fee Collection Type"]] == "ratio value", data[["Transaction Fee Intra-Day Close"]], 0) * 1e-4
    todayClosePershare = ifelse(data[["Fee Collection Type"]] == "absolute", data[["Transaction Fee Intra-Day Close"]], 0)
    return(list(symbol, rate, pershare, todayCloseRate, todayClosePershare))
}

# China financial future exchange settlement patameters
GetCFFEXSettlementFee = function(data) {
    GetValue <- function(x, flag) {
        if (grepl(flag, x)) {
            return(as.numeric(regmatches(x, regexpr(flag, x))))
        } else {
            return(0)
        }
    }
    symbol = gsub("[[:space:]]", "", data[[1]])
    rate = sapply(data[[4]], GetValue, "[0-9.]{4}$") * 1e-4
    pershare = sapply(data[[4]], GetValue, "^[0-9]+")
    dicountRate = PercentToNumeric(data[[6]])
    todayCloseRate = rate * dicountRate
    todayClosePershare = pershare * dicountRate
    return(list(symbol, rate, pershare, todayCloseRate, todayClosePershare))
}


# GuangZhou future exchange settlement patameters
GetGFEXSettlementFee = function(data) {
    symbol = as.character(data$contractId)
    isAbsolute = data$style == "绝对值"
    Assert(identical(data$openFee, data$offsetFee))
    # Assert(identical(data$shortOpenFee, data$shortOffsetFee))
    rate = ifelse(isAbsolute, 0, data$openFee * 0.0001)
    pershare = ifelse(isAbsolute, data$openFee, 0)
    todayCloseRate = ifelse(isAbsolute, 0, data$shortOffsetFee * 0.0001)
    todayClosePershare = ifelse(isAbsolute, data$shortOffsetFee, 0)
    return(list(symbol, rate, pershare, todayCloseRate, todayClosePershare))
}

GetSettlementMargin = function(rawDataRoot, dir, fileName) {
    file = file.path(rawDataRoot, dir, fileName)
    if (!file.exists(file)) {
        return(NULL)
    }

    marketType = "ChinaFuture"
    time = MakeTime(dir, GetTimezone(marketType))
    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    timestamp = GetBeginOfTradingDayNoDS(time, cuttingHour, tradeOnWeekends)
    exchange = file_path_sans_ext(fileName)
    data = ReadSettlementFile(file, exchange)

    if (exchange == "shfe") {
        symbol = as.character(data[["Contract Code"]])
        longRate = data[["Margin Rate for Long Speculation(%)"]] * 0.01
        shortRate = data[["Margin Rate for Short Speculation(%)"]] * 0.01
    } else if (exchange == "dce") {
        symbol = as.character(data[["Code"]])
        longRate = PercentToNumeric(data[["Margin Rate Long Speculation"]])
        shortRate = PercentToNumeric(data[["Margin Rate Short Speculation"]])
    } else if (exchange == "czce") {
        symbol = data[[1]]
        longRate = as.numeric(data[[5]]) * 0.01
        if (startsWith(names(data)[6], "\u6da8\u8dcc")) {
            shortRate = longRate
        } else {
            shortRate = PercentToNumeric(data[[6]])
        }
    } else if (exchange == "cffex") {
        symbol = gsub("[[:space:]]", "", data[[1]])
        longRate = PercentToNumeric(data[[2]])
        shortRate = PercentToNumeric(data[[3]])
    } else if (exchange == "gfex") {
        symbol = data$contractId
        longRate = data$specBuyRate
        shortRate = data$specSellRate
    }

    result = data.table(
        timestamp = timestamp,
        symbol = symbol,
        longrate = longRate,
        shortrate = shortRate
    )
    return(result)
}

#' nodoc
#' @export
CrawlSettlementData = function(rawDataRoot, marketType, ds, begin = GetToday() - days(30), end = GetToday(), force = FALSE) {
    crawlingDates = DateToStr(GetTradingDaySeq(begin, end, ds, marketType))
    if (!force) {
        crawledDates = list.dirs(rawDataRoot, full.names = FALSE, recursive = FALSE)
        crawlingDates = setdiff(crawlingDates, crawledDates)
    }

    if (IsEmpty(crawlingDates)) return(TRUE)
    source_python(system.file("Python", "CrawlSettlementData.py", package = "Demeter"))
    result = CrawlCommission(normalizePath(rawDataRoot), as.list(crawlingDates))
    if (!result) WarningLog("CrawlSettlementData", "Failed to crawl settlement data")
    return(result)
}

#' nodoc
#' @export
CopySettlementData = function(rawDataRoot, dataType, dataSource) {
    marketType = "ChinaFuture"
    tz = GetTimezone(marketType)
    # update rawdata to dataSource
    list = dataSource$ListData(marketType, dataType)

    begin = GetNow()
    end = 0
    if (!IsEmpty(list)) {
        begin = min(list$begin)
        end = max(list$end)
    }

    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)

    getDirTime = function(x) {
        dirTime = GetTradingDayNoDS(MakeTime(x, tz), cuttingHour, cuttingMethod, tradeOnWeekends)
        GetBeginOfTradingDayNoDS(dirTime, cuttingHour, tradeOnWeekends)
    }

    dirs = list.dirs(rawDataRoot, full.names = FALSE, recursive = FALSE)
    for (dir in dirs) {
        time = getDirTime(dir)
        if (time >= begin && time < end) next

        files = c("shfe.csv", "dce.csv", "czce.csv", "cffex.csv", "gfex.csv")
        if (dataType == "Misc/Commission") {
            getFunc = GetSettlementFee
        } else if (dataType == "Misc/Margin") {
            getFunc = GetSettlementMargin
        }

        datas = lapply(files, function(x) getFunc(rawDataRoot, dir, x))

        allData = rbindlist(datas)
        if (nrow(allData) == 0) next

        setorder(allData, timestamp, symbol)
        setattr(allData, "dataType", dataType)
        setattr(allData, "marketType", marketType)

        InfoLog("CopySettlementData", "Copy {dataType} data from {dir} => {dataSource$GetDesc()}")
        dataSource$WriteData(allData)
    }
}

#' nodoc
#' @export
GetTradingDayFromExchRaw = function(exchRawPath, marketType, dataType, dataSourceToSave = NULL) {
    dayDirs = list.dirs(path = exchRawPath, full.names = FALSE, recursive = FALSE)

    tradingDay = c()
    for (day in dayDirs) {
        files = list.files(file.path(exchRawPath, day))
        if (length(files) > 0) tradingDay = c(tradingDay, day)
    }

    tradingDay = as.POSIXct(as.IDate(tradingDay), tz = GetTimezone(marketType))
    data = data.table(tradingday = tradingDay)
    setattr(data, "marketType", marketType)
    setattr(data, "dataType", dataType)
    setattr(data, "symbol", NULL)

    if (!is.null(dataSourceToSave)) {
        dataSourceToSave$WriteData(data)
    }

    return(data)
}

#' nodoc
#' @export
GenCryptoCommission = function(dataSource, makeRate, takeRate) {
    marketType = "Crypto"
    pdts = GetLoadedProducts(marketType)

    cms = data.table(
        timestamp = MakeTime("2022-01-01", GetTimezone(marketType)),
        symbol = pdts$symbol,
        rate = makeRate,
        takerate = takeRate,
        pershare = 0,
        closetodayrate = makeRate,
        closetodaypershare = 0
    )

    setattr(cms, "marketType", marketType)
    setattr(cms, "dataType", "Misc/Commission")
    setattr(cms, "symbol", NULL)

    dataSource$WriteData(cms)
    return(invisible())
}

#' nodoc
#' @export
GenExchangeRebate = function(rawExchangeRebatePath, destFile, beginDate = NULL) {
    marketType = "ChinaFuture"
    dataType = "Misc/ExchangeRebate"
    tz = GetTimezone(marketType)
    cuttingHour = GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)

    if (is.null(beginDate)) {
        updateFile = last(list.files(rawExchangeRebatePath))
    } else {
        exchRebateFile = list.files(rawExchangeRebatePath)
        fileDate = file_path_sans_ext(exchRebateFile)
        updateFile = exchRebateFile[fileDate >= beginDate]
    }

    for (file in updateFile) {
        path = file.path(rawExchangeRebatePath, file)
        InfoLog("UpdateExchangeRebateFile", "update {path} => {destFile}")

        rawData = fread(path, colClasses = c(time = "character"))
        begin = GetBeginOfTradingDayNoDS(MakeTime(rawData$time, tz), cuttingHour, tradeOnWeekends)
        data = data.table(
            timestamp = begin,
            exchange = rawData$exchange,
            broker = rawData$broker,
            product = rawData$product,
            generalRebate = PercentToNumeric(rawData$generalRebate),
            posRebate = PercentToNumeric(rawData$posRebate),
            posIncRebate = PercentToNumeric(rawData$posIncRebate),
            volIncRebate = PercentToNumeric(rawData$volIncRebate),
            corpRebate = PercentToNumeric(rawData$corpRebate),
            indRebate = PercentToNumeric(rawData$indRebate),
            indPropRebate = PercentToNumeric(rawData$indPropRebate),
            inactiveRebate = PercentToNumeric(rawData$inactiveRebate)
        )
        data[is.na(data)] = 0

        cols = c("timestamp", "exchange", "broker", "product")
        setorderv(data, cols)
        setattr(data, "dataType", dataType)
        setattr(data, "marketType", marketType)

        ReadFunc = GetCSVReadFunc(marketType, dataType)
        existing = ReadFunc(destFile)

        if (is.null(existing)) {
            FatalLog("UpdateExchangeRebateFile", "cannot read {destFile}")
        }

        if (!"volIncRebate" %in% colnames(existing)) {
            existing[, volIncRebate := 0]
        }
        if (!"corpRebate" %in% colnames(existing)) {
            existing[, corpRebate := 0]
        }

        # unique existing and new data
        newData = rbind(existing, data)
        newData = unique(newData, fromLast = TRUE, by = cols)
        setorderv(newData, cols)
        newData = ResetVolIncReabteData(marketType, newData, cols)

        RecoverAttrFromData(newData, data)
        newData = RemoveAdjacentDuplicateData(newData)

        CSVWriteMiscFunc(newData, destFile)
    }
}

ResetVolIncReabteData = function(marketType, allData, cols) {
    data = allData[exchange %in% c("SHFE", "INE")]
    # reset volIncRebate to 0 in 26th every next month
    volIncRebateData = data[, .SD[.N], by = product][volIncRebate > 0]

    if (!IsEmpty(volIncRebateData)) {
        cuttingHour = GetTradingDayCuttingHour(marketType)
        cuttingMethod = GetTradingDayCuttingMethod(marketType)
        tradeOnWeekends = GetTradeOnWeekends(marketType)

        td = volIncRebateData[, GetTradingDayNoDS(timestamp, cuttingHour, cuttingMethod, tradeOnWeekends)]
        if (day(td) >= 26) {
            addMonth = months(1)
        } else {
            addMonth = 0
        }
        resetDay = floor_date(td, "month") + days(25) + addMonth
        resetTime = GetBeginOfTradingDayNoDS(resetDay, cuttingHour, tradeOnWeekends)

        resetData = volIncRebateData[, `:=`(timestamp = resetTime, volIncRebate = 0)]
        allData = rbind(allData, resetData)
    }

    # set product value to symbol if symbol volIncRebate is 0
    data[, pd := GetDerivNameOfSymbol("ChinaFuture", product)]
    symbolData = data[pd != product & volIncRebate == 0]
    productIndex = data[symbolData, on = .(timestamp = timestamp, product == pd), which = TRUE]
    symbolData = symbolData[!is.na(productIndex)]
    productData = data[na.omit(productIndex)]

    newSymbolData = productData[, product := symbolData$product]
    newSymbolData[, pd := NULL]

    allData = rbind(allData, newSymbolData)
    allData = unique(allData, fromLast = TRUE, by = cols)
    setorderv(allData, cols)

    return(allData)
}

#' nodoc
#' @export
GetExchangeRebateDiffData = function(newFile, oldFile) {
    ReadFunc = GetCSVReadFunc("ChinaFuture", "Misc/ExchangeRebate")
    oldData = ReadFunc(oldFile)
    newData = ReadFunc(newFile)
    diffData = fsetdiff(newData, oldData)
    if (IsEmpty(diffData)) {
        return(NULL)
    }

    diffIndex = oldData[diffData, on = .(timestamp, exchange, broker, product), which = TRUE]

    newAddedData = diffData[is.na(diffIndex)]
    oldChangedData = oldData[na.omit(diffIndex)]
    newChangedData = fsetdiff(diffData, newAddedData)

    preData = lapply(seq_len(nrow(newAddedData)), \(i) {
        data = oldData[na.omit(oldData[newAddedData[i], on = .(exchange, broker, product), which = TRUE])]
        data = last(data[timestamp < newAddedData[i]$timestamp])
        if (IsEmpty(data)) {
            return(newAddedData[NA])
        }
        return(data)
    })
    preData = rbindlist(preData)

    allDiffData = rbind(cbind(newChangedData, oldChangedData), cbind(newAddedData, preData))

    valueCols = setdiff(colnames(allDiffData), c("timestamp", "exchange", "broker", "product"))
    diffCol = sapply(valueCols, \(col) {
        colIdx = which(col == colnames(allDiffData))
        all(allDiffData[[colIdx[1]]] == allDiffData[[colIdx[2]]])
    })
    diffCol = names(diffCol[diffCol == FALSE])
    return(list(data = allDiffData, col = diffCol))
}
