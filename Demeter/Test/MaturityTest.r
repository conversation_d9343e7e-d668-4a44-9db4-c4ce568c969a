library(Demeter)
InitDemeter()
componentName = "MaturityTest"
InitLogger()
ds = GetFSTDataSource("~/Data/MarketData")
cf = "ChinaFuture"
marketType = cf
dataSource = ds
noThrow = TRUE


symbolsA = c("IC1809", "IC1810", "IC1811", "IC1812", "IC1901", "IC1902", "IC1903",
            "ag1809", "ag1810", "ag1811", "ag1812", "ag1901", "ag1902", "ag1903")

symbolsB = c("IC2501", "IC2502", "IC2503", "IC2504", "IC2505", "IC2512", "IC2601",
            "ag2501", "ag2502", "ag2503", "ag2504", "ag2505", "ag2512", "ag2601")

ret1 = GetMaturityTime(cf, symbolsA, ds, TRUE)
ret2 = GetMaturityTimeNoDS(cf, symbolsA)
ret3 = GetDaysToMaturity(cf, symbolsA, ds, GetNow())
ret4 = GetDaysToMaturityNoDS(cf, symbolsA)
ret5 = GetTradingDaysToMaturity(cf, symbolsA, ds)
ret6 = GetTradingDaysToMaturityNoDS(cf, symbolsA)
setnames(ret2, old = names(ret2)[3], new = glue("{names(ret2)[3]}NoDS"))
setnames(ret4, old = names(ret4)[3], new = glue("{names(ret4)[3]}NoDS"))
setnames(ret6, old = names(ret6)[3], new = glue("{names(ret6)[3]}NoDS"))
retList = list(ret1, ret2, ret3, ret4, ret5, ret6)
mergedRet = retList[[1]][, c(1, 2)]
for (i in seq_along(retList)) {
    colName = names(retList[[i]])[3]
    mergedRet[, (colName) := retList[[i]][, 3]]
}
InfoLog(componentName, "Result: \n{DataTableToStr(mergedRet)}")


ret1 = GetMaturityTime(cf, symbolsB, ds, TRUE)
ret2 = GetMaturityTimeNoDS(cf, symbolsB)
ret3 = GetDaysToMaturity(cf, symbolsB, ds, GetNow(), TRUE)
ret4 = GetDaysToMaturityNoDS(cf, symbolsB)
ret5 = GetTradingDaysToMaturity(cf, symbolsB, ds, GetNow(), TRUE)
ret6 = GetTradingDaysToMaturityNoDS(cf, symbolsB)
setnames(ret2, old = names(ret2)[3], new = glue("{names(ret2)[3]}NoDS"))
setnames(ret4, old = names(ret4)[3], new = glue("{names(ret4)[3]}NoDS"))
setnames(ret6, old = names(ret6)[3], new = glue("{names(ret6)[3]}NoDS"))
retList = list(ret1, ret2, ret3, ret4, ret5, ret6)
mergedRet = retList[[1]][, c(1, 2)]
for (i in seq_along(retList)) {
    colName = names(retList[[i]])[3]
    mergedRet[, (colName) := retList[[i]][, 3]]
}
InfoLog(componentName, "Result: \n{DataTableToStr(mergedRet)}")


e = try_capture_stack({
    GetMaturityTime(cf, symbolsB, ds, FALSE)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "GetMaturityTime stop ok.")
} else {
    ErrorLog(componentName, "GetMaturityTime stop failed.")
}

e = try_capture_stack({
    GetDaysToMaturity(cf, symbolsB, ds, GetNow(), FALSE)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "GetDaysToMaturity stop ok.")
} else {
    ErrorLog(componentName, "GetDaysToMaturity stop failed.")
}

e = try_capture_stack({
    GetTradingDaysToMaturity(cf, symbolsB, ds, GetNow(), FALSE)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "GetTradingDaysToMaturity stop ok.")
} else {
    ErrorLog(componentName, "GetTradingDaysToMaturity stop failed.")
}