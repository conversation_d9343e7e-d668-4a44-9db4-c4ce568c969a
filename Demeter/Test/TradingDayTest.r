if (TRUE) {
library(Demeter)
SetPreferredConcurrency()
InitDemeter()
InitLogger()
componentName = "TradingDayTest"
ds = GetFSTDataSource("~/Data/MarketData")
cf = "ChinaFuture"
cs = "ChinaStock"
dataSource = ds
marketType = cf
F = FALSE
T = TRUE
InitTradingSession(ds)

t1 = MakeTime("2025-01-27 17:00:01")
t2 = MakeTime("2025-01-31 16:00:00")
t3 = MakeTime("2025-02-05 16:00:00")
t4 = MakeTime("2025-02-07 17:00:00")
t5 = MakeTime("2025-01-28 00:00:00")
t6 = MakeTime("2025-01-24 00:00:00")
t7 = MakeTime("2025-01-17 12:00:00")
t8 = MakeTime("2025-01-17 22:00:00")
t9 = MakeTime("2025-01-18 12:00:00")
t10 = MakeTime("2025-01-18 22:00:00")
t11 = MakeTime("2095-01-20 12:00:00")
t12 = MakeTime("1990-01-20 22:00:00")

time = c(t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11, t12)
}
# test IsTradingDay
Error = FALSE
e = try_capture_stack({
    ret = IsTradingDay(time, ds, cf)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "IsTradingDay cf stop ok")
} else {
    ErrorLog(componentName, "IsTradingDay cf stop not ok")
    Error = TRUE
}
ret = IsTradingDay(time, ds, cf, TRUE)
answer = c(FALSE, FALSE, TRUE, FALSE, FALSE, TRUE, TRUE, FALSE, FALSE, FALSE, TRUE, FALSE)
if (all(ret == answer)) {
    InfoLog(componentName, "IsTradingDay cf ok")
} else {
    ErrorLog(componentName, "IsTradingDay cf not ok")
    Error = TRUE
}

ret = IsTradingDay(time, ds, cs, TRUE)
answer = c(TRUE, FALSE, TRUE, TRUE, FALSE, TRUE, TRUE, TRUE, FALSE, FALSE, TRUE, FALSE)
if (all(ret == answer)) {
    InfoLog(componentName, "IsTradingDay cs ok")
} else {
    ErrorLog(componentName, "IsTradingDay cs not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ IsTradingDay not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ IsTradingDay ok. ---------------------")
}


# test GetTradingDay
Error = FALSE
e = try_capture_stack({
    ret = GetTradingDay(time, ds, cf)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "GetTradingDay cf stop ok")
} else {
    ErrorLog(componentName, "GetTradingDay cf stop not ok")
    Error = TRUE
}
ret = GetTradingDay(time, ds, cf, TRUE)
answer = c( "2025-02-05 CST", "2025-02-05 CST",
            "2025-02-05 CST", "2025-02-10 CST",
            "2025-02-05 CST", "2025-01-24 CST",
            "2025-01-17 CST", "2025-01-20 CST",
            "2025-01-20 CST", "2025-01-20 CST",
            "2200-01-01 CST", "1990-01-01 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetTradingDay cf ok")
} else {
    ErrorLog(componentName, "GetTradingDay cf not ok")
    Error = TRUE
}

ret = GetTradingDay(time, ds, cs, TRUE)
answer = c( "2025-01-27 CST", "2025-01-27 CST",
            "2025-02-05 CST", "2025-02-07 CST",
            "2025-01-27 CST", "2025-01-24 CST",
            "2025-01-17 CST", "2025-01-17 CST",
            "2025-01-17 CST", "2025-01-17 CST",
            "2200-01-01 CST", "1990-01-01 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetTradingDay cs ok")
} else {
    ErrorLog(componentName, "GetTradingDay cs not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ GetTradingDay not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ GetTradingDay ok. ---------------------")
}


# test AddTradingDays
Error = FALSE
e = try_capture_stack({
    ret = AddTradingDays(time, 1, ds, cf)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "AddTradingDays cf stop ok")
} else {
    ErrorLog(componentName, "AddTradingDays cf stop not ok")
    Error = TRUE
}
ret = AddTradingDays(time, 1, ds, cf, TRUE)
answer = c( "2025-02-05 17:00:01 CST", "2025-02-06 16:00:00 CST",
            "2025-02-06 16:00:00 CST", "2025-02-10 17:00:00 CST",
            "2025-02-06 00:00:00 CST", "2025-01-27 00:00:00 CST",
            "2025-01-20 12:00:00 CST", "2025-01-20 22:00:00 CST",
            "2025-01-21 12:00:00 CST", "2025-01-20 22:00:00 CST",
            "2200-01-01 00:00:00 CST", "1990-01-01 00:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "AddTradingDays cf ok")
} else {
    ErrorLog(componentName, "AddTradingDays cf not ok")
    Error = TRUE
}

ret = AddTradingDays(time, 1, ds, cs, TRUE)
answer = c( "2025-02-05 17:00:01 CST", "2025-02-05 16:00:00 CST",
            "2025-02-06 16:00:00 CST", "2025-02-10 17:00:00 CST",
            "2025-02-05 00:00:00 CST", "2025-01-27 00:00:00 CST",
            "2025-01-20 12:00:00 CST", "2025-01-20 22:00:00 CST",
            "2025-01-20 12:00:00 CST", "2025-01-20 22:00:00 CST",
            "2200-01-01 00:00:00 CST", "1990-01-01 00:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "AddTradingDays cs ok")
} else {
    ErrorLog(componentName, "AddTradingDays cs not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ AddTradingDays not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ AddTradingDays ok. ---------------------")
}


# test GetBeginOfTradingDay
Error = FALSE
ret = GetBeginOfTradingDayNoDS(time, 17, F)
answer = c( "2025-01-27 17:00:00 CST", "2025-01-30 17:00:00 CST",
            "2025-02-04 17:00:00 CST", "2025-02-07 17:00:00 CST",
            "2025-01-27 17:00:00 CST", "2025-01-23 17:00:00 CST",
            "2025-01-16 17:00:00 CST", "2025-01-17 17:00:00 CST",
            "2025-01-17 17:00:00 CST", "2025-01-17 17:00:00 CST",
            "2095-01-19 17:00:00 CST", "1990-01-19 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfTradingDayNoDS cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingDayNoDS cf not ok")
    Error = TRUE
}

e = try_capture_stack({
    ret = GetBeginOfTradingDay(time, ds, cf)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "GetBeginOfTradingDay cf stop ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingDay cf stop not ok")
    Error = TRUE
}
ret = GetBeginOfTradingDay(time, ds, cf, noThrow = TRUE)
answer = c( "2025-01-27 17:00:00 CST", "2025-01-27 17:00:00 CST",
            "2025-01-27 17:00:00 CST", "2025-02-07 17:00:00 CST",
            "2025-01-27 17:00:00 CST", "2025-01-23 17:00:00 CST",
            "2025-01-16 17:00:00 CST", "2025-01-17 17:00:00 CST",
            "2025-01-17 17:00:00 CST", "2025-01-17 17:00:00 CST",
            "2200-01-01 17:00:00 CST", "1990-01-01 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfTradingDay ds cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingDay ds cf not ok")
    Error = TRUE
}

ret = GetBeginOfTradingDayNoDS(time, 0, F)
answer = c( "2025-01-27 CST", "2025-01-31 CST",
            "2025-02-05 CST", "2025-02-07 CST",
            "2025-01-28 CST", "2025-01-24 CST",
            "2025-01-17 CST", "2025-01-17 CST",
            "2025-01-17 CST", "2025-01-17 CST",
            "2095-01-20 CST", "1990-01-19 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfTradingDayNoDS cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingDayNoDS cs not ok")
    Error = TRUE
}

ret = GetBeginOfTradingDay(time, ds, cs, TRUE)
answer = c( "2025-01-27 CST", "2025-01-27 CST",
            "2025-02-05 CST", "2025-02-07 CST",
            "2025-01-27 CST", "2025-01-24 CST",
            "2025-01-17 CST", "2025-01-17 CST",
            "2025-01-17 CST", "2025-01-17 CST",
            "2200-01-01 CST", "1990-01-01 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfTradingDay ds cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingDay ds cs not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ GetBeginOfTradingDay not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ GetBeginOfTradingDay ok. ---------------------")
}


# test GetBeginOfNextTradingDay
Error = FALSE
ret = GetBeginOfNextTradingDayNoDS(time, 17, F)
answer = c( "2025-01-28 17:00:00 CST", "2025-01-31 17:00:00 CST",
            "2025-02-05 17:00:00 CST", "2025-02-10 17:00:00 CST",
            "2025-01-28 17:00:00 CST", "2025-01-24 17:00:00 CST",
            "2025-01-17 17:00:00 CST", "2025-01-20 17:00:00 CST",
            "2025-01-20 17:00:00 CST", "2025-01-20 17:00:00 CST",
            "2095-01-20 17:00:00 CST", "1990-01-22 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingDayNoDS cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingDayNoDS cf not ok")
    Error = TRUE
}

e = try_capture_stack({
    ret = GetBeginOfNextTradingDay(time, ds, cf)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "GetBeginOfNextTradingDay cf stop ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingDay cf stop not ok")
    Error = TRUE
}
ret = GetBeginOfNextTradingDay(time, ds, cf, TRUE)
answer = c( "2025-02-05 17:00:00 CST", "2025-02-05 17:00:00 CST",
            "2025-02-05 17:00:00 CST", "2025-02-10 17:00:00 CST",
            "2025-02-05 17:00:00 CST", "2025-01-24 17:00:00 CST",
            "2025-01-17 17:00:00 CST", "2025-01-20 17:00:00 CST",
            "2025-01-20 17:00:00 CST", "2025-01-20 17:00:00 CST",
            "2200-01-01 17:00:00 CST", "1990-01-01 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingDay ds cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingDay ds cf not ok")
    Error = TRUE
}

ret = GetBeginOfNextTradingDayNoDS(time, 0, F)
answer = c( "2025-01-28 CST", "2025-02-03 CST",
            "2025-02-06 CST", "2025-02-10 CST",
            "2025-01-29 CST", "2025-01-27 CST",
            "2025-01-20 CST", "2025-01-20 CST",
            "2025-01-20 CST", "2025-01-20 CST",
            "2095-01-21 CST", "1990-01-22 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingDayNoDS cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingDayNoDS cs not ok")
    Error = TRUE
}

ret = GetBeginOfNextTradingDay(time, ds, cs, TRUE)
answer = c( "2025-02-05 CST", "2025-02-05 CST",
            "2025-02-06 CST", "2025-02-10 CST",
            "2025-02-05 CST", "2025-01-27 CST",
            "2025-01-20 CST", "2025-01-20 CST",
            "2025-01-20 CST", "2025-01-20 CST",
            "2200-01-01 CST", "1990-01-01 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingDay ds cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingDay ds cs not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ GetBeginOfNextTradingDay not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ GetBeginOfNextTradingDay ok. ---------------------")
}


# test GetBeginOfTradingMonth
Error = FALSE
ret = GetBeginOfTradingMonthNoDS(time, 17, 2, F)
answer = c( "2024-12-31 17:00:00 CST", "2024-12-31 17:00:00 CST",
            "2025-01-31 17:00:00 CST", "2025-01-31 17:00:00 CST",
            "2024-12-31 17:00:00 CST", "2024-12-31 17:00:00 CST",
            "2024-12-31 17:00:00 CST", "2024-12-31 17:00:00 CST",
            "2024-12-31 17:00:00 CST", "2024-12-31 17:00:00 CST",
            "2094-12-31 17:00:00 CST", "1989-12-29 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfTradingMonthNoDS cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingMonthNoDS cf not ok")
    Error = TRUE
}

e = try_capture_stack({
    ret = GetBeginOfTradingMonth(time, ds, cf)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "GetBeginOfTradingMonth cf stop ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingMonth cf stop not ok")
    Error = TRUE
}
ret = GetBeginOfTradingMonth(time, ds, cf, TRUE)
answer = c( "2024-12-31 17:00:00 CST", "2024-12-31 17:00:00 CST",
            "2025-01-27 17:00:00 CST", "2025-01-27 17:00:00 CST",
            "2024-12-31 17:00:00 CST", "2024-12-31 17:00:00 CST",
            "2024-12-31 17:00:00 CST", "2024-12-31 17:00:00 CST",
            "2024-12-31 17:00:00 CST", "2024-12-31 17:00:00 CST",
            "2200-01-01 17:00:00 CST", "1990-01-01 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfTradingMonth ds cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingMonth ds cf not ok")
    Error = TRUE
}

ret = GetBeginOfTradingMonthNoDS(time, 0, 1, F)
answer = c( "2025-01-01 CST", "2025-01-01 CST",
            "2025-02-03 CST", "2025-02-03 CST",
            "2025-01-01 CST", "2025-01-01 CST",
            "2025-01-01 CST", "2025-01-01 CST",
            "2025-01-01 CST", "2025-01-01 CST",
            "2095-01-03 CST", "1990-01-01 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfTradingMonthNoDS cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingMonthNoDS cs not ok")
    Error = TRUE
}

ret = GetBeginOfTradingMonth(time, ds, cs, TRUE)
answer = c( "2025-01-02 CST", "2025-01-02 CST",
            "2025-02-05 CST", "2025-02-05 CST",
            "2025-01-02 CST", "2025-01-02 CST",
            "2025-01-02 CST", "2025-01-02 CST",
            "2025-01-02 CST", "2025-01-02 CST",
            "2200-01-01 CST", "1990-01-01 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfTradingMonth ds cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingMonth ds cs not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ GetBeginOfTradingMonth not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ GetBeginOfTradingMonth ok. ---------------------")
}


# test GetBeginOfNextTradingMonth
Error = FALSE
ret = GetBeginOfNextTradingMonthNoDS(time, 17, 2, F)
answer = c( "2025-01-31 17:00:00 CST", "2025-01-31 17:00:00 CST",
            "2025-02-28 17:00:00 CST", "2025-02-28 17:00:00 CST",
            "2025-01-31 17:00:00 CST", "2025-01-31 17:00:00 CST",
            "2025-01-31 17:00:00 CST", "2025-01-31 17:00:00 CST",
            "2025-01-31 17:00:00 CST", "2025-01-31 17:00:00 CST",
            "2095-01-31 17:00:00 CST", "1990-01-31 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingMonthNoDS cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingMonthNoDS cf not ok")
    Error = TRUE
}

e = try_capture_stack({
    ret = GetBeginOfNextTradingMonth(time, ds, cf)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "GetBeginOfNextTradingMonth cf stop ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingMonth cf stop not ok")
    Error = TRUE
}
ret = GetBeginOfNextTradingMonth(time, ds, cf, TRUE)
answer = c( "2025-01-27 17:00:00 CST", "2025-01-27 17:00:00 CST",
            "2025-02-28 17:00:00 CST", "2025-02-28 17:00:00 CST",
            "2025-01-27 17:00:00 CST", "2025-01-27 17:00:00 CST",
            "2025-01-27 17:00:00 CST", "2025-01-27 17:00:00 CST",
            "2025-01-27 17:00:00 CST", "2025-01-27 17:00:00 CST",
            "2200-01-01 17:00:00 CST", "1990-01-01 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingMonth ds cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingMonth ds cf not ok")
    Error = TRUE
}

ret = GetBeginOfNextTradingMonthNoDS(time, 0, 1, F)
answer = c( "2025-02-03 CST", "2025-02-03 CST",
            "2025-03-03 CST", "2025-03-03 CST",
            "2025-02-03 CST", "2025-02-03 CST",
            "2025-02-03 CST", "2025-02-03 CST",
            "2025-02-03 CST", "2025-02-03 CST",
            "2095-02-01 CST", "1990-02-01 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingMonthNoDS cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingMonthNoDS cs not ok")
    Error = TRUE
}

ret = GetBeginOfNextTradingMonth(time, ds, cs, TRUE)
answer = c( "2025-02-05 CST", "2025-02-05 CST",
            "2025-03-03 CST", "2025-03-03 CST",
            "2025-02-05 CST", "2025-02-05 CST",
            "2025-02-05 CST", "2025-02-05 CST",
            "2025-02-05 CST", "2025-02-05 CST",
            "2200-01-01 CST", "1990-01-01 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingMonth ds cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingMonth ds cs not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ GetBeginOfNextTradingMonth not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ GetBeginOfNextTradingMonth ok. ---------------------")
}


t0 = MakeTime("2015-02-05 17:00:00")
t1 = MakeTime("2016-02-05 17:00:00")
t2 = MakeTime("2017-02-05 17:00:00")
t3 = MakeTime("2018-02-05 17:00:00")
t4 = MakeTime("2019-02-05 17:00:00")
t5 = MakeTime("2020-02-05 17:00:00")
t6 = MakeTime("2021-02-05 17:00:00")
t7 = MakeTime("2022-02-05 17:00:00")
t8 = MakeTime("2023-02-05 17:00:00")
t9 = MakeTime("2024-02-05 17:00:00")
t10 = MakeTime("2095-02-05 17:00:00")
t11 = MakeTime("1997-02-05 17:00:00")
time = c(t0, t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11)
# test GetBeginOfTradingYear
Error = FALSE
ret = GetBeginOfTradingYearNoDS(time, 17, 2, F)
answer = c( "2014-12-31 17:00:00 CST", "2015-12-31 17:00:00 CST",
            "2016-12-30 17:00:00 CST", "2017-12-29 17:00:00 CST",
            "2018-12-31 17:00:00 CST", "2019-12-31 17:00:00 CST",
            "2020-12-31 17:00:00 CST", "2021-12-31 17:00:00 CST",
            "2022-12-30 17:00:00 CST", "2023-12-29 17:00:00 CST",
            "2094-12-31 17:00:00 CST", "1996-12-31 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfTradingYearNoDS cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingYearNoDS cf not ok")
    Error = TRUE
}

e = try_capture_stack({
    ret = GetBeginOfTradingYear(time, ds, cf)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "GetBeginOfTradingYear cf stop ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingYear cf stop not ok")
    Error = TRUE
}
ret = GetBeginOfTradingYear(time, ds, cf, TRUE)
answer = c( "2015-01-02 17:00:00 CST", "2015-12-30 17:00:00 CST",
            "2016-12-29 17:00:00 CST", "2017-12-28 17:00:00 CST",
            "2018-12-27 17:00:00 CST", "2019-12-30 17:00:00 CST",
            "2020-12-30 17:00:00 CST", "2021-12-30 17:00:00 CST",
            "2022-12-29 17:00:00 CST", "2023-12-28 17:00:00 CST",
            "2200-01-01 00:00:00 CST", "1990-01-01 00:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfTradingYear ds cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingYear ds cf not ok")
    Error = TRUE
}

ret = GetBeginOfTradingYearNoDS(time, 0, 1, F)
answer = c( "2015-01-01 CST", "2016-01-01 CST",
            "2017-01-02 CST", "2018-01-01 CST",
            "2019-01-01 CST", "2020-01-01 CST",
            "2021-01-01 CST", "2022-01-03 CST",
            "2023-01-02 CST", "2024-01-01 CST",
            "2095-01-03 CST", "1997-01-01 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfTradingYearNoDS cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingYearNoDS cs not ok")
    Error = TRUE
}

ret = GetBeginOfTradingYear(time, ds, cs, TRUE)
answer = c( "2015-01-05 00:00:00 CST", "2016-01-04 00:00:00 CST",
            "2017-01-03 00:00:00 CST", "2018-01-02 00:00:00 CST",
            "2019-01-02 00:00:00 CST", "2020-01-02 00:00:00 CST",
            "2021-01-04 00:00:00 CST", "2022-01-04 00:00:00 CST",
            "2023-01-03 00:00:00 CST", "2024-01-02 00:00:00 CST",
            "2200-01-01 00:00:00 CST", "1990-01-01 00:00:00 CST")
if (all(ret == MakeTime(answer))) {
    InfoLog(componentName, "GetBeginOfTradingYear ds cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfTradingYear ds cs not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ GetBeginOfTradingYear not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ GetBeginOfTradingYear ok. ---------------------")
}


# test GetBeginOfNextTradingYear
Error = FALSE
ret = GetBeginOfNextTradingYearNoDS(time, 17, 2, F)
answer = c( "2015-12-31 17:00:00 CST", "2016-12-30 17:00:00 CST",
            "2017-12-29 17:00:00 CST", "2018-12-31 17:00:00 CST",
            "2019-12-31 17:00:00 CST", "2020-12-31 17:00:00 CST",
            "2021-12-31 17:00:00 CST", "2022-12-30 17:00:00 CST",
            "2023-12-29 17:00:00 CST", "2024-12-31 17:00:00 CST",
            "2095-12-30 17:00:00 CST", "1997-12-31 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingYearNoDS cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingYearNoDS cf not ok")
    Error = TRUE
}

e = try_capture_stack({
    ret = GetBeginOfNextTradingYear(time, ds, cf)
}, environment())
if (is.error(e)) {
    InfoLog(componentName, "GetBeginOfNextTradingYear cf stop ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingYear cf stop not ok")
    Error = TRUE
}
ret = GetBeginOfNextTradingYear(time, ds, cf, TRUE)
answer = c( "2015-12-30 17:00:00 CST", "2016-12-29 17:00:00 CST",
            "2017-12-28 17:00:00 CST", "2018-12-27 17:00:00 CST",
            "2019-12-30 17:00:00 CST", "2020-12-30 17:00:00 CST",
            "2021-12-30 17:00:00 CST", "2022-12-29 17:00:00 CST",
            "2023-12-28 17:00:00 CST", "2024-12-30 17:00:00 CST",
            "2200-01-01 00:00:00 CST", "1990-01-01 00:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingYear ds cf ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingYear ds cf not ok")
    Error = TRUE
}

ret = GetBeginOfNextTradingYearNoDS(time, 0, 1, F)
answer = c( "2016-01-01 CST", "2017-01-02 CST",
            "2018-01-01 CST", "2019-01-01 CST",
            "2020-01-01 CST", "2021-01-01 CST",
            "2022-01-03 CST", "2023-01-02 CST",
            "2024-01-01 CST", "2025-01-01 CST",
            "2096-01-02 CST", "1998-01-01 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingYearNoDS cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingYearNoDS cs not ok")
    Error = TRUE
}

ret = GetBeginOfNextTradingYear(time, ds, cs, TRUE)
answer = c( "2016-01-04 00:00:00 CST", "2017-01-03 00:00:00 CST",
            "2018-01-02 00:00:00 CST", "2019-01-02 00:00:00 CST",
            "2020-01-02 00:00:00 CST", "2021-01-04 00:00:00 CST",
            "2022-01-04 00:00:00 CST", "2023-01-03 00:00:00 CST",
            "2024-01-02 00:00:00 CST", "2025-01-02 00:00:00 CST",
            "2200-01-01 00:00:00 CST", "1990-01-01 00:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetBeginOfNextTradingYear ds cs ok")
} else {
    ErrorLog(componentName, "GetBeginOfNextTradingYear ds cs not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ GetBeginOfNextTradingYear not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ GetBeginOfNextTradingYear ok. ---------------------")
}



# test GetTradingDaySeq
Error = FALSE
begin = MakeTime("2025-01-23 17:00:00")
end = MakeTime("2025-02-12 17:00:00")
ret = GetTradingDaySeq(begin, end, ds, cf)
answer = c( "2025-01-23 17:00:00 CST", "2025-01-24 17:00:00 CST",
            "2025-01-27 17:00:00 CST", "2025-02-05 17:00:00 CST",
            "2025-02-06 17:00:00 CST", "2025-02-07 17:00:00 CST",
            "2025-02-10 17:00:00 CST", "2025-02-11 17:00:00 CST",
            "2025-02-12 17:00:00 CST", "2025-02-13 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetTradingDaySeq cf ok")
} else {
    ErrorLog(componentName, "GetTradingDaySeq cf not ok")
    Error = TRUE
}

ret = GetTradingDaySeq(begin, end, ds, cs)
answer = c( "2025-01-23 CST", "2025-01-24 CST", 
            "2025-01-27 CST", "2025-02-05 CST",
            "2025-02-06 CST", "2025-02-07 CST", 
            "2025-02-10 CST", "2025-02-11 CST",
            "2025-02-12 CST", "2025-02-13 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetTradingDaySeq cs ok")
} else {
    ErrorLog(componentName, "GetTradingDaySeq cs not ok")
    Error = TRUE
}

ret = GetTradingDaySeqNoDS(begin, end, 17, FALSE)
answer = c( "2025-01-23 17:00:00 CST", "2025-01-24 17:00:00 CST",
            "2025-01-27 17:00:00 CST", "2025-01-28 17:00:00 CST",
            "2025-01-29 17:00:00 CST", "2025-01-30 17:00:00 CST",
            "2025-01-31 17:00:00 CST", "2025-02-03 17:00:00 CST",
            "2025-02-04 17:00:00 CST", "2025-02-05 17:00:00 CST",
            "2025-02-06 17:00:00 CST", "2025-02-07 17:00:00 CST",
            "2025-02-10 17:00:00 CST", "2025-02-11 17:00:00 CST",
            "2025-02-12 17:00:00 CST", "2025-02-13 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetTradingDaySeqNoDS cf ok")
} else {
    ErrorLog(componentName, "GetTradingDaySeqNoDS cf not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ GetTradingDaySeq not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ GetTradingDaySeq ok. ---------------------")
}



# test GetTradingMonthSeq
Error = FALSE
begin = MakeTime("2024-11-12 17:00:00")
end = MakeTime("2025-06-04 17:00:00")
ret = GetTradingMonthSeq(begin, end, ds, cf)
answer = c( "2024-10-31 17:00:00 CST", "2024-11-29 17:00:00 CST",
            "2024-12-31 17:00:00 CST", "2025-01-27 17:00:00 CST",
            "2025-02-28 17:00:00 CST", "2025-03-31 17:00:00 CST",
            "2025-04-30 17:00:00 CST", "2025-05-30 17:00:00 CST",
            "2025-06-30 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetTradingMonthSeq cf ok")
} else {
    ErrorLog(componentName, "GetTradingMonthSeq cf not ok")
    Error = TRUE
}

ret = GetTradingMonthSeq(begin, end, ds, cf)
if (all(ret == answer)) {
    InfoLog(componentName, "GetTradingMonthSeq cf ok")
} else {
    ErrorLog(componentName, "GetTradingMonthSeq cf not ok")
    Error = TRUE
}


ret = GetTradingMonthSeq(begin, end, ds, cs)
answer = c( "2024-11-01 CST", "2024-12-02 CST",
            "2025-01-02 CST", "2025-02-05 CST",
            "2025-03-03 CST", "2025-04-01 CST",
            "2025-05-06 CST", "2025-06-03 CST",
            "2025-07-01 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetTradingMonthSeq cs ok")
} else {
    ErrorLog(componentName, "GetTradingMonthSeq cs not ok")
    Error = TRUE
}

ret = GetTradingMonthSeqNoDS(begin, end, 17, 2, FALSE)
answer = c( "2024-10-31 17:00:00 CST", "2024-11-29 17:00:00 CST",
            "2024-12-31 17:00:00 CST", "2025-01-31 17:00:00 CST",
            "2025-02-28 17:00:00 CST", "2025-03-31 17:00:00 CST",
            "2025-04-30 17:00:00 CST", "2025-05-30 17:00:00 CST",
            "2025-06-30 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetTradingMonthSeqNoDS cf ok")
} else {
    ErrorLog(componentName, "GetTradingMonthSeqNoDS cf not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ GetTradingMonthSeq not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ GetTradingMonthSeq ok. ---------------------")
}



# test GetTradingYearSeq
Error = FALSE
begin = MakeTime("2015-06-03")
end = MakeTime("2024-04-02")
ret = GetTradingYearSeq(begin, end, ds, cf)
answer = c( "2015-01-02 17:00:00 CST", "2015-12-30 17:00:00 CST",
            "2016-12-29 17:00:00 CST", "2017-12-28 17:00:00 CST",
            "2018-12-27 17:00:00 CST", "2019-12-30 17:00:00 CST",
            "2020-12-30 17:00:00 CST", "2021-12-30 17:00:00 CST",
            "2022-12-29 17:00:00 CST", "2023-12-28 17:00:00 CST",
            "2024-12-30 17:00:00 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetTradingMonthSeq cf ok")
} else {
    ErrorLog(componentName, "GetTradingMonthSeq cf not ok")
    Error = TRUE
}

ret = GetTradingYearSeq(begin, end, ds, cs)
answer = c( "2015-01-05 CST", "2016-01-04 CST",
            "2017-01-03 CST", "2018-01-02 CST",
            "2019-01-02 CST", "2020-01-02 CST",
            "2021-01-04 CST", "2022-01-04 CST",
            "2023-01-03 CST", "2024-01-02 CST",
            "2025-01-02 CST")
if (all(ret == answer)) {
    InfoLog(componentName, "GetTradingYearSeqNoDS cs ok")
} else {
    ErrorLog(componentName, "GetTradingYearSeqNoDS cs not ok")
    Error = TRUE
}
if (Error) {
    ErrorLog(componentName, "------------------------ GetTradingYearSeq not ok. ---------------------")
} else {
    InfoLog(componentName, "------------------------ GetTradingYearSeq ok. ---------------------")
}
