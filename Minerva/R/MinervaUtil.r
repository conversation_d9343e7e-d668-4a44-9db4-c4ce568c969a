#' product list is a data table with following columns:
#' marketType
#' symbol, optional, could be symbol or derivative name, if missing get all symbols during the begin and end
#' dataType, optional, if missing, use Demeter::GetDefaultDataType(marketType)
#'
#' @param productList the product list to query.
#'                     if derivative name is provided, read concrete symbol from loaded products.
#' @param begin, end: the period to query. they're converted to trading day, both inclusive
#' @param withOI:  default is TRUE. if TRUE, the return data contains openinterest column
#' @param withRatio: default is TRUE. if TRUE,the return data contains vol ratio and openinterest ratio columns
#' @param filterData: default is TRUE. if TRUE, the market data used internal is fitered by the FiterData function
#' @param adjVol: default is TRUE. if TRUE, the return data contains the adjTotalvol and the adjVolRatio column

#' @return data table with columns: timestamp, marketType, symbol, totalvol, openinterest(optional), oiRatio(optional), volRatio(option)
#' @export
GetTotalvol = function(productList, begin, end, withOI = TRUE, withRatio = TRUE, filterData = TRUE, adjVol = TRUE) {

    AssertNotNull(productList)
    AssertNotNull(productList$dataSource)
    AssertNotNull(productList$marketType)

    result = c()

    for (i in 1:nrow(productList)) {

        if (is.list(productList$dataSource)) {
            dataSource = productList$dataSource[[i]]
        } else {
            # only one product
            dataSource = productList$dataSource
        }

        symbol = productList$symbol[i]
        mkt = productList$marketType[i]
        dataType = productList$dataType[i]

        if (is.null(dataType)) {
            dataType = "Bar/60"
            defaultDataType = Demeter::GetDefaultDataType(mkt)
        } else {
            defaultDataType = NULL
        }

        cuttingHour = Demeter::GetTradingDayCuttingHour(mkt)
        cuttingMethod = Demeter::GetTradingDayCuttingMethod(mkt)
        tradeOnWeekends = Demeter::GetTradeOnWeekends(mkt)

        b = GetBeginOfTradingDayNoDS(begin, cuttingHour, tradeOnWeekends)
        e = GetBeginOfNextTradingDayNoDS(end, cuttingHour, tradeOnWeekends)
        beginTradingDay = GetTradingDayNoDS(b, cuttingHour, cuttingMethod, tradeOnWeekends)

        if (!is.null(symbol)) {
            symDerivName = GetDerivNameOfSymbol(mkt, symbol)
            if (symDerivName == symbol) {
                symbol = GetLoadedProducts()[marketType == mkt][derivativeName == symDerivName]$symbol
            }
        } else {
            if (beginTradingDay == GetToday()) {
                symbol = GetLoadedProducts(mkt)$symbol
            } else {
                listInfo = dataSource$ListData(mkt, dataType)
                if (IsEmpty(listInfo) && !is.null(defaultDataType)) {
                    listInfo = dataSource$ReadData(mkt, defaultDataType)
                }
                symbol = listInfo[begin <= e & end >= b, symbol]
            }
        }

        symTotalvol = rbindlist(lapply(symbol, function(sym) {
            tempDataType = dataType
            data = dataSource$ReadData(mkt, tempDataType, sym, b, e)
            if (IsEmpty(data) && !is.null(defaultDataType)) {
                data = dataSource$ReadData(mkt, defaultDataType, sym, b, e)
                tempDataType = defaultDataType
            }
            if (filterData) data = Demeter::FilterData(dataSource, data)
            if (IsEmpty(data)) return(NULL)

            timeCol = ifelse(tempDataType == "Bar/60", "closetime", "exchtime")
            data[, timestamp := GetBeginOfTradingDayNoDS(get(timeCol), cuttingHour, tradeOnWeekends)]

            if (adjVol) {
                data[, ssType := GetSessionType(get(timeCol), mkt, sym)]

                if (tempDataType == "Bar/60") {
                    data = data[, .(symbol = sym, totalvol = sum(vol), openinterest = last(openinterest), closepx = last(close)),
                                by = c("timestamp", "ssType")]
                } else {
                    data = data[, .(symbol = sym, totalvol = last(totalvol), openinterest = last(openinterest), closepx = last(lastpx)),
                                by = c("timestamp", "ssType")]
                    totalvolDiff = data[, diff(c(0, totalvol)), by = timestamp][, c(2)]
                    data[, totalvol := totalvolDiff]
                }

                ret = data[, .(symbol = sym, totalvol = sum(totalvol), closepx = last(closepx)), by = timestamp]
                ret[, adjTotalvol := totalvol]

                sessionsType = GetSession(ret$timestamp, mkt, sym)
                sessionsType = lapply(sessionsType, function(x) x$type)

                completeSession = sessionsType[[which.max(sapply(sessionsType, length))]]
                incompleteSessionTypeIdx = which(sapply(sessionsType, function(x)!identical(completeSession, x)))
                incompleteSessionTypeList = unique(sessionsType[incompleteSessionTypeIdx])
                completeTimestamp = ret[-incompleteSessionTypeIdx]$timestamp

                for (ist in incompleteSessionTypeList) {
                    incomplteTotalvol = data[timestamp %in% completeTimestamp, .(totalvol = ifelse(ssType %in% ist, totalvol, 0)), by = timestamp]
                    incomplteTotalvol = incomplteTotalvol[, .(totalvol = sum(totalvol)), by = timestamp]
                    completeTotalvol = ret[timestamp %in% completeTimestamp, totalvol]
                    ratio = mean(completeTotalvol / incomplteTotalvol$totalvol)
                    if (!is.finite(ratio)) next
                    incompleteIdx = which(sapply(sessionsType, function(x) identical(ist, x)))
                    ret[incompleteIdx, adjTotalvol := round(adjTotalvol * ratio)]
                }
            } else {
                if (tempDataType == "Bar/60") {
                    ret = data[, .(symbol = sym, totalvol = sum(vol), closepx = last(close)), by = timestamp]
                } else {
                    ret = data[, .(symbol = sym, totalvol = last(totalvol), closepx = last(lastpx)), by = timestamp]
                }
            }

            if (withOI) {
                oi = data[, last(openinterest), by = timestamp][, c(2)]
                ret[, openinterest := oi]
            }

            return(ret)
        }))

        if (!IsEmpty(symTotalvol)) {
            symTotalvol[, marketType := mkt]
            result = rbind(result, symTotalvol)
        }
    }

    if (!IsEmpty(result)) {
        setcolorder(result, c("timestamp", "marketType"))
        setorder(result, timestamp)

        if (withRatio) {
            result[, derivName := GetDerivNameOfSymbol(marketType, symbol)]
            result[, volRatio := round(totalvol / first(totalvol), 3), by = .(timestamp, derivName)]
            if (adjVol) {
                result[, adjVolRatio := round(adjTotalvol / first(adjTotalvol), 3), by = .(timestamp, derivName)]
            }
            if (withOI) {
                result[, oiRatio := round(openinterest / first(openinterest), 3), by = .(timestamp, derivName)]
            }
            result[, derivName := NULL]
        }
    }

    result[]
    return(result)
}

#' GetThickness
#' @param productList the product list to query.
#'                     if derivative name is provided, read concrete symbol from loaded products.
#' @param begin, end: the period to query. they're converted to trading day, both inclusive
#' @param filterData: default is TRUE. if TRUE, the market data used internal is fitered by the FiterData function
#' @return data table with columns: timestamp, marketType, symbol, arithThickness, geoThickness, spread
#' @export
GetThickness = function(productList, begin, end, filterData = TRUE) {

    AssertNotNull(productList)
    AssertNotNull(productList$dataSource)
    AssertNotNull(productList$marketType)

    result = c()
    for (i in 1:nrow(productList)) {

        if (is.list(productList$dataSource)) {
            dataSource = productList$dataSource[[i]]
        } else {
            # only one product
            dataSource = productList$dataSource
        }

        symbol = productList$symbol[i]
        mkt = productList$marketType[i]
        dataType = productList$dataType[i]
        transformer = productList$transformer[[i]]

        if (is.null(dataType)) dataType = Demeter::GetDefaultDataType(mkt)

        if (dataType == "Tick") {
            bidvolCol = "bidvol"
            askvolCol = "bidvol"
            askpxCol = "askpx"
            bidpxCol = "bidpx"
        } else if (dataType == "Orderbook5") {
            bidvolCol = "bidvol0"
            askvolCol = "bidvol0"
            askpxCol = "askpx0"
            bidpxCol = "bidpx0"
        } else {
            NotImplemented()
        }

        cuttingHour = Demeter::GetTradingDayCuttingHour(mkt)
        cuttingMethod = Demeter::GetTradingDayCuttingMethod(mkt)
        tradeOnWeekends = Demeter::GetTradeOnWeekends(mkt)

        b = GetBeginOfTradingDayNoDS(begin, cuttingHour, tradeOnWeekends)
        e = GetBeginOfNextTradingDayNoDS(end, cuttingHour, tradeOnWeekends)
        beginTradingDay = GetTradingDayNoDS(begin, cuttingHour, cuttingMethod, tradeOnWeekends)

        if (is.null(symbol)) {
            if (beginTradingDay == GetToday()) {
                symbol = GetLoadedProducts(mkt)$symbol
            } else {
                listInfo = dataSource$ListData(mkt, dataType)
                symbol = listInfo[begin <= e & end >= b, symbol]
            }
        } else {
            symDerivName = GetDerivNameOfSymbol(mkt, symbol)
            if (!is.na(symDerivName) && symDerivName == symbol) {
                symbol = GetLoadedProducts(mkt)[derivativeName == symDerivName]$symbol
            }
        }

        symbolThickness = rbindlist(lapply(symbol, function(sym) {
            data = dataSource$ReadData(mkt, dataType, sym, b, e)
            if (filterData) data = Demeter::FilterData(dataSource, data)
            if (!is.null(transformer)) data = transformer(data)
            if (IsEmpty(data)) return(NULL)
            symTickSize = GetTickSize(mkt, sym)

            data[, timestamp := GetBeginOfTradingDayNoDS(exchtime, cuttingHour, tradeOnWeekends)]
            data[, spread := (get(askpxCol) - get(bidpxCol)) / symTickSize]
            ret = data[, .(symbol = sym,
                        arithThickness = mean((get(bidvolCol) + get(askvolCol)) / (spread + 1), na.rm = TRUE),
                        geoThickness = mean((get(bidvolCol) + get(askvolCol)) ^ (1 / (spread + 1)), na.rm = TRUE),
                        spread = mean(spread, na.rm = TRUE)),
                    by = timestamp]
            return(ret)
        }))

        result = rbind(result, symbolThickness)
    }

    if (!IsEmpty(result)) {
        setorder(result, timestamp)
        result[, marketType := mkt]
        setcolorder(result, c("timestamp", "marketType"))
    }

    result[]
    return(result)
}

#' GetVolatility
#' @param productList: the product list to query.
#'                     if derivative name is provided, amplify with the latest major product
#' @param begin, end: the period to query. they're converted to trading day, both inclusive
#' @param barSize: if the dataSource doesn't support given bar size, read Bar/60 and compress.
#'                  if Bar/60 is absent, return NULL
#' @param majorOnly: if true only major symbols are calculated
#' @param volatilityFunc: function(bar) get volatility from bar
#' @return data table with columns: timestamp, marketType, symbol, volatility
#' @export
GetVolatility = function(productList, begin, end,
                          barSize = 60, majorOnly = TRUE, volatilityFunc = NULL) {

    AssertNotNull(productList)
    AssertNotNull(productList$dataSource)
    AssertNotNull(productList$marketType)

    barSize = BarSizeToSecond(barSize)
    if (is.null(volatilityFunc)) {
        volatilityFunc = function(bar) { abs(bar$open - bar$close) }
    }

    result = c()
    for (i in 1:nrow(productList)) {

        if (is.list(productList$dataSource)) {
            dataSource = productList$dataSource[[i]]
        } else {
            # only one product
            dataSource = productList$dataSource
        }

        symbol = productList$symbol[i]
        mkt = productList$marketType[i]
        dataType = paste0("Bar/", barSize)
        cuttingHour = Demeter::GetTradingDayCuttingHour(mkt)
        cuttingMethod = Demeter::GetTradingDayCuttingMethod(mkt)
        tradeOnWeekends = Demeter::GetTradeOnWeekends(mkt)

        b = GetBeginOfTradingDayNoDS(begin, cuttingHour, tradeOnWeekends)
        e = GetBeginOfNextTradingDayNoDS(end, cuttingHour, tradeOnWeekends)
        majorData = NULL
        if (is.null(symbol)) {
            bTradingDay = GetTradingDayNoDS(begin, cuttingHour, cuttingMethod, tradeOnWeekends)
            eTradingDay = GetTradingDayNoDS(end, cuttingHour, cuttingMethod, tradeOnWeekends)

            if (majorOnly) {

                allMajorData = dataSource$ReadData(mkt, "Misc/Major")
                tradingDayData = dataSource$ReadData(mkt, "Misc/TradingDay")
                tradingDay = tradingDayData[tradingday >= bTradingDay & tradingday <= eTradingDay, tradingday]
                allBeginTradingDay = GetBeginOfTradingDayNoDS(tradingDay, cuttingHour, tradeOnWeekends)
                majorData = GetMajorValue(allMajorData, allBeginTradingDay, allowCrossProductTimeNSymbol = TRUE)
                majorData = majorData[!is.na(major)]
                symbol = unique(majorData$major)
            } else if (bTradingDay == GetToday()) {
                symbol = GetLoadedProducts(mkt)$symbol
            } else {
                listInfo = dataSource$ListData(mkt, dataType)
                if (IsEmpty(listInfo) && dataType != "Bar/60") {
                    listInfo = dataSource$ListData(mkt, "Bar/60")
                }
                symbol = listInfo[begin <= e & end >= b, symbol]
            }
        } else {
            symDerivName = GetDerivNameOfSymbol(mkt, symbol)
            if (symDerivName == symbol) {
                symbol = GetLoadedProducts(mkt)[derivativeName == symDerivName]$symbol
            }
        }

        symbolVolatility = rbindlist(lapply(symbol, function(sym) {
            bar = dataSource$ReadData(mkt, dataType, sym, b, e)
            if (IsEmpty(bar) && dataType != "Bar/60") {
                bar60 = dataSource$ReadData(mkt, "Bar/60", sym, b, e)
                if (IsEmpty(bar60)) return(NULL)
                bar = CompressBar(bar60, barSize)
            }
            if (IsEmpty(bar)) return(NULL)

            bar[, timestamp := GetBeginOfTradingDayNoDS(closetime, cuttingHour, tradeOnWeekends)]
            bar[, volatility := volatilityFunc(bar)]
            ret = bar[, .(symbol = sym, volatility = sqrt(mean(volatility ^ 2, na.rm = TRUE))), by = timestamp]

            if (!is.null(majorData)) {
                ret = ret[timestamp %in% majorData[sym == major, timestamp]]
            }

            return(ret)
        }))

        result = rbind(result, symbolVolatility)
    }

    if (!IsEmpty(result)) {
        result[, marketType := mkt]
        result[, derivName := GetDerivNameOfSymbol(marketType, symbol)]
        setorder(result, timestamp)
        setcolorder(result, c("timestamp", "marketType", "derivName"))
    }

    result[]
    return(result)
}
