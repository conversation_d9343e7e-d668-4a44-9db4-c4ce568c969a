#' nodoc
#' @export
MonitorCFVol = function(dataSource, monitorDay, watchList = NULL, blackList = NULL, shortAvg = 5, longAvg = 20) {
    marketType = "ChinaFuture"
    tz = Demeter::GetTimezone(marketType)
    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    cuttingMethod = Demeter::GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)

    monitorDay = MakeTime(monitorDay)
    monitorTradingDay = as.POSIXct(DateToStr(monitorDay), tz = tz)

    td = dataSource$ReadData(marketType, "Misc/TradingDay")
    if (!monitorTradingDay %in% td$tradingday) {
        WarningLog("MonitorCFVol", "Monitor cf vol failed, because of the monitor day {monitorDay} is not a trading day")
        return(NULL)
    }

    monitorDayBegin = GetBeginOfTradingDayNoDS(monitorDay, cuttingHour, tradeOnWeekends)
    shortDayBegin = AddTradingDays(monitorDayBegin, 1 - shortAvg, dataSource, marketType)
    longDayBegin = AddTradingDays(monitorDayBegin, 1 - longAvg, dataSource, marketType)

    productList = NewProductList(marketType, NULL, dataSource = dataSource)
    allSymbolTvol = GetTotalvol(productList, begin = longDayBegin, end = monitorDayBegin)
    if (IsEmpty(allSymbolTvol)) return(NULL)

    allSymbolTvol[, derivname := GetDerivNameOfSymbol(marketType, symbol)]
    allProdTvol = allSymbolTvol[, .(totalvol = sum(totalvol), adjTotalvol = sum(adjTotalvol)), by = c("timestamp", "derivname")]

    if (!IsEmpty(blackList)) allProdTvol = allProdTvol[!derivname %in% blackList]

    monitorDayProdTvol = allProdTvol[timestamp >= monitorDayBegin]
    shortDayProdTvol = allProdTvol[timestamp >= shortDayBegin][derivname %in% monitorDayProdTvol$derivname]
    longDayProdTvol = allProdTvol[timestamp >= longDayBegin][derivname %in% monitorDayProdTvol$derivname]

    shortDayAvgColName = paste0(shortAvg, "dAvg")
    shortDayProdTvol = shortDayProdTvol[, setNames(lapply(.SD, mean), shortDayAvgColName), by = derivname, .SDcols = "adjTotalvol"]
    longDayAvgColName = paste0(longAvg, "dAvg")
    longDayProdTvol = longDayProdTvol[, setNames(lapply(.SD, mean), longDayAvgColName), by = derivname, .SDcols = "adjTotalvol"]

    ret = monitorDayProdTvol[shortDayProdTvol, on = .(derivname)]
    ret = ret[longDayProdTvol, on = .(derivname)]

    ret[, fastInc:= (adjTotalvol - get(shortDayAvgColName)) / get(shortDayAvgColName)]
    ret[, stableInc := (get(shortDayAvgColName) - get(longDayAvgColName)) / get(longDayAvgColName)]

    ret[, exchange := FindExchange(marketType, derivname)]
    ret[, timestamp := GetTradingDayNoDS(timestamp, cuttingHour, cuttingMethod, tradeOnWeekends)]
    setcolorder(ret, c("timestamp", "exchange", "derivname"))
    setorder(ret, "timestamp", "exchange", "derivname")

    if (!IsEmpty(watchList)) {
        idx = which(ret$derivname %in% watchList)
        if (length(idx) != 0) ret = rbind(ret[idx], ret[-c(idx)])
    }
    roundCols = names(ret)[grepl("(Avg|Inc)$", names(ret))]
    ret[, c(roundCols) := lapply(.SD, function(x) round(x, 3)), .SDcols = roundCols]

    return(ret)
}

#' nodoc
#' @export
MonitorMajor = function(dataSource, marketType, monitorDay, watchList = NULL, whiteList = NULL, blackList = NULL, nDaysAvg = 5) {
    tz = Demeter::GetTimezone(marketType)
    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    cuttingMethod = Demeter::GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)

    monitorDay = MakeTime(monitorDay)
    monitorTradingDay = as.POSIXct(DateToStr(monitorDay), tz = tz)

    td = dataSource$ReadData(marketType, "Misc/TradingDay")
    if (!monitorTradingDay %in% td$tradingday) {
        WarningLog("MonitorMajor", "Get {marketType} major failed, because of the monitor day {monitorDay} is not a trading day")
        return(NULL)
    }

    monitorDayBegin = GetBeginOfTradingDayNoDS(monitorDay, cuttingHour, tradeOnWeekends)
    begin = AddTradingDays(monitorDayBegin, 1 - nDaysAvg, dataSource, marketType)

    allMajor = dataSource$ReadData(marketType, "Misc/Major", NULL)
    majorData = GetMajorValue(allMajor, monitorDayBegin)
    if (!IsEmpty(whiteList)) majorData = majorData[derivname %in% whiteList]
    if (!IsEmpty(blackList)) majorData = majorData[!derivname %in% blackList]

    majorSymbol = c(majorData$major0, majorData$major, majorData$major2)
    majorSymbol = majorSymbol[!is.na(majorSymbol)]
    productList = NewProductList(marketType, majorSymbol, dataSource = dataSource)
    symbolTotalvol = GetTotalvol(productList, begin = begin, end = monitorDayBegin, withOI = TRUE, withRatio = FALSE, adjVol = FALSE)

    if(IsEmpty(symbolTotalvol)){
        return(NULL)
    }

    symbolTotalvol = symbolTotalvol[, .(timestamp, symbol, totalvol, openinterest)]

    majorData = majorData[, .(timestamp, derivname, major0, major, major2)]
    majorData = rbindlist(lapply(unique(symbolTotalvol$timestamp), function(t) {
        data = copy(majorData)
        data[, timestamp := t]
    }))

    majorData = symbolTotalvol[majorData, on = .(timestamp, symbol == major2)]
    names(majorData)[2:4] = c("major2", "tvol2", "oi2")
    majorData = symbolTotalvol[majorData, on = .(timestamp, symbol == major)]
    names(majorData)[2:4] = c("major", "tvol", "oi")
    majorData = symbolTotalvol[majorData, on = .(timestamp, symbol == major0)]
    names(majorData)[2:4] = c("major0", "tvol0", "oi0")

    # totalvol ratio
    majorData[, vol0Ratio := tvol0 / tvol]
    majorData[, volRatio := tvol2 / tvol]
    nDaysAvgVolColName = paste0("volRatio", nDaysAvg, "dAvg")
    nDaysAvgVolRatio = majorData[, setNames(lapply(.SD, mean), nDaysAvgVolColName), by = derivname, .SDcols = "volRatio"]

    curMajorData = majorData[timestamp == monitorDayBegin]
    ret = curMajorData[nDaysAvgVolRatio, on = .(derivname)]
    ret[, volRatioChg := volRatio / get(nDaysAvgVolColName) - 1]

    # openinterest ratio
    majorData[, oi0Ratio := oi0 / oi]
    majorData[, oiRatio := oi2 / oi]
    nDaysAvgOIColName = paste0("oiRatio", nDaysAvg, "dAvg")
    nDaysAvgOIRatio = majorData[, setNames(lapply(.SD, mean), nDaysAvgOIColName), by = derivname, .SDcols = "oiRatio"]

    ret = cbind(ret, majorData[timestamp == monitorDayBegin][, c("oi0Ratio", "oiRatio")])
    ret = ret[nDaysAvgOIRatio, on = .(derivname)]
    ret[, oiRatioChg := oiRatio / get(nDaysAvgOIColName) - 1]

    ret[, timestamp := GetTradingDayNoDS(timestamp, cuttingHour, cuttingMethod, tradeOnWeekends)]
    ret[, exchange := FindExchange(marketType, derivname)]
    setcolorder(ret, c("timestamp", "exchange", "derivname"))

    # sort
    setorder(ret, "derivname")
    if (!IsEmpty(watchList)) {
        idx = which(ret$derivname %in% watchList)
        if (length(idx) != 0) ret = rbind(ret[idx], ret[-c(idx)])
    }
    setorder(ret, "timestamp", "exchange")

    ratioIdx = grepl("Ratio", names(ret))
    roundCols = names(ret)[ratioIdx]
    ret[, c(roundCols) := lapply(.SD, function(x) round(x, 3)), .SDcols = roundCols]

    names(ret)[ratioIdx] = paste0("%", gsub("Ratio", "", names(ret)[ratioIdx]))

    return(ret)
}

#' nodoc
#' @export
WatchMajor = function(monitorDay, productList) {

    ret = data.table()
    for (i in 1:nrow(productList)) {

        if (is.list(productList$dataSource)) {
            dataSource = productList$dataSource[[i]]
        }
        else {
            dataSource = productList$dataSource
        }

        sym = productList$symbol[i]
        mkt = productList$marketType[i]
        majorData = MonitorMajor(dataSource, mkt, monitorDay, whiteList = sym, nDaysAvg = 1)
        if(IsEmpty(majorData)) next
        majorData = majorData[, .(timestamp, exchange, derivname, major, tvol, oi, major2, tvol2, oi2, `%vol`, `%oi`)]
        method = productList$method[i]
        value = productList$value[i]
        if(method == "vol") alert = majorData[["%vol"]] > value
        if(method == "oi") alert = majorData[["%oi"]] > value
        majorData[, method := method]
        majorData[, alert := alert]
        ret = rbind(ret, majorData)
    }

    return(ret)
}

#' nodoc
#' @export
MonitorCFVolConstituent = function(dataSource, monitorDay, whiteList = NULL, blackList = NULL, leastRatio = 0.05) {
    marketType = "ChinaFuture"
    tz = Demeter::GetTimezone(marketType)
    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)

    monitorDay = MakeTime(monitorDay)
    monitorTradingDay = as.POSIXct(DateToStr(monitorDay), tz = tz)
    begin = GetBeginOfTradingDayNoDS(monitorDay, cuttingHour, tradeOnWeekends)

    td = dataSource$ReadData(marketType, "Misc/TradingDay")
    if (!monitorTradingDay %in% td$tradingday) {
        WarningLog("MonitorCFVolConstituent", "Monitor cf vol constituent failed, because of the monitor day {monitorDay} is not a trading day")
        return(NULL)
    }

    productList = NewProductList(marketType, NULL, dataSource = dataSource)

    ret = GetTotalvol(productList, begin = begin, end = begin, withRatio = FALSE, adjVol = FALSE)
    if (IsEmpty(ret)) return(NULL)

    ret = ret[, .(timestamp, symbol, totalvol, openinterest)]
    ret[, derivname := GetDerivNameOfSymbol(marketType, symbol)]
    ret[, exchange := FindExchange(marketType, symbol)]
    setcolorder(ret, c("timestamp", "exchange", "derivname", "symbol"))

    if (!IsEmpty(blackList)) ret = ret[!derivname %in% blackList]

    ret[, derTotalvol := sum(totalvol), by = derivname]
    ret[, ratio := ifelse(derTotalvol == 0, 0, totalvol / derTotalvol)]
    ret[, derTotaloi := sum(openinterest), by = derivname]
    ret[, oiRatio := ifelse(derTotaloi == 0, 0, openinterest / derTotaloi)]
    setorder(ret, exchange, symbol, - ratio)

    if (!is.null(leastRatio)) {
        validRatio = ret[ratio >= leastRatio]
        invalidRatio = ret[ratio < leastRatio]
        other = invalidRatio[, .(timestamp = unique(timestamp),
                                  exchange = unique(exchange),
                                  symbol = "other",
                                  totalvol = sum(totalvol),
                                  openinterest = sum(openinterest),
                                  derTotalvol = unique(derTotalvol),
                                  derTotaloi = unique(derTotaloi),
                                  ratio = sum(ratio),
                                  oiRatio = sum(oiRatio)), by = derivname]

        setcolorder(other, colnames(ret))
        ret = rbind(validRatio, other)
        setorder(ret, exchange, derivname)
    }

    if (!IsEmpty(whiteList)) ret = ret[derivname %in% whiteList]

    return(ret)
}

#' nodoc
#' @export
MonitorMDOrder = function(solidDataRoot, monitorDay) {
    tz = Demeter::GetTimezone("ChinaFuture")
    monitorDay = DateToStr(as.Date(monitorDay, tz = tz))
    marketType = c("ChinaFuture")
    dataType = c("Tick", "Orderbook5")
    ret = data.table()
    for (mkt in marketType) {
        for (dt in dataType) {
            mdOrderData = GetMDOrder(solidDataRoot, mkt, paste0("Solid", dt), monitorDay)
            if (IsEmpty(mdOrderData)) next
            orderStr = sapply(1:nrow(mdOrderData), function(x) {
                order = mdOrderData$mdOrder[[x]]
                paste0(head(order, 12), collapse = " ")
            })
            mdOrderData[, mdOrder := orderStr]
            dt = cbind(marketType = mkt, dataType = dt, mdOrderData)
            ret = rbind(ret, dt)
        }
    }

    return(ret)
}

#' nodoc
#' @export
MonitorMarginChg = function(dataSource, monitorDay) {
    marketType = "ChinaFuture"
    tz = Demeter::GetTimezone(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)

    monitorDay = MakeTime(monitorDay)
    monitorDay = as.POSIXct(DateToStr(monitorDay), tz = tz)
    td = dataSource$ReadData(marketType, "Misc/TradingDay")
    if (!monitorDay %in% td$tradingday) {
        WarningLog("MonitorMarginChg", "Monitor margin chgange failed, because of the monitor day {monitorDay} is not a trading day")
        return(NULL)
    }

    preDay = AddTradingDays(monitorDay, -1, dataSource, marketType)

    allMarginData = dataSource$ReadData(marketType, "Misc/Margin")
    preMargin = GetExchangeMargin(allMarginData, preDay)
    monitorDayMargin = GetExchangeMargin(allMarginData, monitorDay)

    combineData = monitorDayMargin[preMargin, on = .(symbol)]
    colnames(combineData) = gsub("i\\.", "pre", colnames(combineData))

    monitorData = combineData[longrate != prelongrate | shortrate != preshortrate]
    if (IsEmpty(monitorData)) return(NULL)

    colnames(monitorData) = c("timestamp", "symbol", "LRate", "SRate", "-timestamp", "-LRate", "-SRate")
    monitorData[, exch := FindExchange(marketType, symbol)]

    setcolorder(monitorData, c("timestamp", "exch", "symbol"))
    setorder(monitorData, exch, symbol)
    return(monitorData)
}

#' nodoc
#' @export
MonitorCommissionChg = function(dataSource, monitorDay) {
    marketType = "ChinaFuture"
    tz = Demeter::GetTimezone(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)

    monitorDay = MakeTime(monitorDay)
    monitorDay = as.POSIXct(DateToStr(monitorDay), tz = tz)
    td = dataSource$ReadData(marketType, "Misc/TradingDay")
    if (!monitorDay %in% td$tradingday) {
        WarningLog("MonitorCommissionChg", "Monitor commission chgange failed, because of the monitor day {monitorDay} is not a trading day")
        return(NULL)
    }

    preDay = AddTradingDays(monitorDay, -1, dataSource, marketType)

    allCmsData = dataSource$ReadData(marketType, "Misc/Commission")
    preCms = GetExchangeCommission(allCmsData, preDay)
    monitorDayCms = GetExchangeCommission(allCmsData, monitorDay)

    combineData = monitorDayCms[preCms, on = .(symbol)]

    monitorData = combineData[rate != i.rate |
                                pershare != i.pershare |
                                closetodayrate != i.closetodayrate |
                                closetodaypershare != i.closetodaypershare]
    if (IsEmpty(monitorData)) return(NULL)

    monitorDayChg = monitorDayCms[symbol %in% monitorData$symbol]
    monitorDayChgOnPx = rbindlist(lapply(1:nrow(monitorDayChg), function(x)
        GetCommissionOnPx(monitorDayChg[x], dataSource, monitorDay)))
    shortNames = c("timestamp", "symbol", "onPx", "onPxCT", "rate", "perShare", "rateCT", "perShareCT")
    colnames(monitorDayChgOnPx) = shortNames

    preDayChg = preCms[symbol %in% monitorData$symbol]
    preDayChgOnPx = rbindlist(lapply(1:nrow(preDayChg), function(x)
        GetCommissionOnPx(preDayChg[x], dataSource, preDay)))
    colnames(preDayChgOnPx) = shortNames
    preDayChgOnPx[, symbol := NULL]
    colnames(preDayChgOnPx) = paste0("-", colnames(preDayChgOnPx))

    monitorData = cbind(monitorDayChgOnPx, preDayChgOnPx)

    monitorData[, exch := FindExchange(marketType, symbol)]
    setcolorder(monitorData, c("timestamp", "exch", "symbol"))

    setorder(monitorData, exch, symbol)
    return(monitorData)
}

#' nodoc
#' @export
MonitorCFLiquidity = function(dataSource, monitorDay) {
    marketType = "ChinaFuture"

    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)

    begin = GetBeginOfTradingDayNoDS(MakeTime(monitorDay), cuttingHour, tradeOnWeekends)

    productList = NewProductList(marketType, NULL, dataSource = dataSource)

    ret = GetThickness(productList, begin = begin, end = begin)
    if (IsEmpty(ret)) return(NULL)
    ret[, marketType := NULL]

    symbolTvol = GetTotalvol(productList, begin = begin, end = begin, withOI = FALSE, withRatio = FALSE)

    if (!IsEmpty(symbolTvol)) {
        totalvol = symbolTvol[match(ret$symbol, symbol), totalvol]
        ret = ret[, totalvol := totalvol]
    }

    ret[, exchange := FindExchange(marketType, symbol)]
    ret[, arithThickness := format(arithThickness, scientific = FALSE, digits = 3)]
    ret[, geoThickness := format(geoThickness, scientific = FALSE, digits = 3)]
    setcolorder(ret, c("timestamp", "exchange", "symbol"))
    setorder(ret, exchange, symbol)
    return(ret)
}

#' nodoc
#' @export
MonitorCFVolatility = function(dataSource, monitorDay, barSize = 60,
    shortAvg = 5, longAvg = 20, whiteList = NULL, blackList = NULL) {

    marketType = "ChinaFuture"
    monitorDay = MakeTime(monitorDay)

    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    cuttingMethod = Demeter::GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)

    monitorDayBegin = GetBeginOfTradingDayNoDS(monitorDay, cuttingHour, tradeOnWeekends)
    shortBegin = AddTradingDays(monitorDayBegin, 1 - shortAvg, dataSource, marketType)
    longBegin = AddTradingDays(monitorDayBegin, 1 - longAvg, dataSource, marketType)

    productList = NewProductList(marketType, NULL, dataSource = dataSource)
    result = GetVolatility(productList, longBegin, monitorDayBegin, barSize)

    if (!IsEmpty(whiteList)) result = result[derivName %in% whiteList]
    if (!IsEmpty(blackList)) result = result[!derivName %in% blackList]

    shortName = paste0(shortAvg, "dAvg")
    longName = paste0(longAvg, "dAvg")

    monitorDayResult = result[timestamp == monitorDayBegin]
    shortDayResult = result[timestamp >= shortBegin & derivName %in% monitorDayResult$derivName][, mean(volatility), by = derivName]
    names(shortDayResult)[2] = shortName
    longDayResult = result[derivName %in% monitorDayResult$derivName][, mean(volatility), by = derivName]
    names(longDayResult)[2] = longName

    result = monitorDayResult[shortDayResult, on = .(derivName)]
    result = result[longDayResult, on = .(derivName)]

    result[, fastInc := (volatility - get(shortName)) / get(shortName)]
    result[, stableInc := (get(shortName) - get(longName)) / get(longName)]
    result[, exchange := FindExchange(marketType, symbol)]
    result[, marketType := NULL]

    roundCols = names(result)[grepl("(volatility|Avg|Inc)$", names(result))]
    result[, c(roundCols) := lapply(.SD, function(x) round(x, 3)), .SDcols = roundCols]

    setcolorder(result, c("timestamp", "exchange"))

    result = result[order(result$stableInc, decreasing = TRUE, na.last = TRUE)]
    setorder(result, exchange)

    return(result)
}