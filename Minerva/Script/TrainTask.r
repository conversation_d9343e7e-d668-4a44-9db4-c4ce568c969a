NewTrainTask = function(...) {
    args = list()

    args$dataSource = NULL
    args$logSource = NULL

    args$server = NULL
    args$strategy = NULL
    args$logMsgType = NULL
    args$GetFormula = DefaultGetFormula
    args$begin = NULL
    args$end = NULL

    args$dataDir = NULL # path to save X & Y for each strategy
    args$stateDir = NULL # path to save torch state for each strategy
    args$outputDir = NULL # path to save output model, in ONNX format

    args$calcFactor = NULL # function(md, logs)

    args$getTrainer = NULL # function() -> prometheus trainer object. fill the constructor args
    args = modifyList(args, list(...))
    class(args) = c("TrainTask", class(args))
    return(args)
}

StartTrainTask = function(task) {
    # read log
    AllStgLog = ReadTaskLog(task)
    strategyLog = AllStgLog[[1]]

    formula = if (!is.null(task$GetFormula)) task$GetFormula(strategyLog) else NULL
    for (msgType in names(strategyLog)) {
        strategyLog[[msgType]] = FixLog(strategyLog[[msgType]], formula)
    }

    # read market data
    marketData = ReadTaskMarketData(task, strategyLog, formula)

    # generate transaction
    transaction = GenerateTaskTransaction(task, strategyLog, formula, marketData)

    # align
    alignedData = AlignTaskData(strategyLog, formula, marketData, transaction)

    # calc factors
    x = do.call(CalcFactor, alignedData)
    y = do.call(CalcY, alignedData)

    # run trainer
    StartTrain(task, x, y)
}

ReadTaskLog = function(task) {
    dataSource = task$dataSource
    logSource = task$logSource
    begin = task$begin
    end = task$end
    server = task$server
    logMsgType = task$logMsgType

    cuttingHour = Ceres::GetTradingDayCuttingHour(server)
    tradeOnWeekends = Ceres::GetTradeOnWeekends(server)
    begin = GetBeginOfTradingDayNoDS(begin, cuttingHour, tradeOnWeekends)
    end = GetBeginOfNextTradingDayNoDS(end, cuttingHour, tradeOnWeekends)

    strategy = task$strategy
    svrStgLog = ReadComponentLog(logSource, server, strategy, logMsgType, begin, end)
    if (IsEmpty(svrStgLog)) {
        InfoLog("ReadTaskLog", "{s} has no log during {begin} ~ {end}")
        return(NULL)
    }

    InfoLog("ReadTaskLog", "Reading log done")
    return(svrStgLog)
}


ReadTaskMarketData = function(task, strategyLog, formula) {
    dataSource = task$dataSource
    begin = task$begin
    end = task$end

    logData = strategyLog[c("OrderExec", "OrderXed", "OrderFill")]
    logData = logData[lengths(logData) != 0]

    if (IsEmpty(logData)) {
        return(NULL)
    }

    begin = min(do.call(c, lapply(logData, \(x) first(x$Timestamp))))
    end = max(do.call(c, lapply(logData, \(x) last(x$Timestamp))))

    formula = as.data.table(formula)
    ModifyDataTableByVarArgs(formula, dataSource = dataSource)
    legMD = GetAlignedLegs(formula, begin, end)
    arbTick = GetArbTick(formula, begin, end, filterData = TRUE, nSlots = 2, legMD = legMD)

    marketData = list(notional = arbTick)
    marketData = modifyList(marketData, legMD)
    InfoLog("ReadTaskMarketData", "Reading log done")
    return(marketData)
}


ReadTaskTransaction = function(task, strategyLog, formula, marketData) {

    legs = list()

    marketType = unique(strategyLog$OrderFill$MarketType)
    for (mkt in marketType) {
        mktStgFillLog = strategyLog$OrderFill[MarketType == mkt]
        symbol = unique(mktStgFillLog$Symbol)

        for (sym in symbol) {
            leg = list()

            symLog = lapply(strategyLog, function(log) {
                if (!IsEmpty(log$MarketType)) log = log[MarketType == mkt]
                if (!IsEmpty(log$Symbol)) log = log[Symbol == sym]
                return(log)
            })

            mktName = ifelse(length(marketType) == 1, "", mkt)
            legName = paste0(mktName, sym)

            legs[[legName]] = CalcTransaction(
                marketData,
                symLog,
                arbWeightCandidates = c()
            )
        }
    }

    transaction = CalcTransaction(
            marketData,
            strategyLog,
            arbWeightCandidates = NULL,
            formula
    )

    # ret = list(strategyTransaction = transaction)
    # ret = modifyList(ret, legs)
    return(transaction)
}

AlignTaskData = function(strategyLog, formula, marketData, transaction) {
    primaryLeg = first(formula$symbol)
    alignedTime = marketData[[primaryLeg]]$exchtime

    alignedStrategyLog = AlignStrategyLog(strategyLog, alignedTime)
    alignedTrans = AlignTransaction(transaction, alignedTime)
    return(list(strategyLog = alignedStrategyLog, marketData = marketData, transaction = alignedTrans))
}

AlignStrategyLog = function(strategyLog, alignedTime) {
    alignedStrategyLog = lapply(strategyLog, \(log) {
        DefaultAlignTrainTaskData(log, alignedTime)
    })
    return(alignedStrategyLog)
}

DefaultAlignTrainTaskData = function(data, alignedTime, timeCol = "Timestamp") {
    dataIndex = SelectIndex(data[[timeCol]], alignedTime)
    groupIndex = split(seq_along(dataIndex), dataIndex)
    firstDataIndex = sapply(groupIndex, first)

    index = rep(NaN, length(alignedTime))
    index[unique(dataIndex)] = firstDataIndex # select the first matched data as aligned data
    oriAttr = attributes(data)
    alignedData = as.list(data)
    alignedData = lapply(alignedData, "[", index)
    alignedData[[timeCol]] = alignedTime
    setDT(alignedData)
    RecoverAttr(alignedData, oriAttr)
    return(alignedData)
}


AlignTransaction = function(transactionm, alignedTime) {
    transList = GetOpenAndCloseTransaction(transaction)
    openTrans = AlignOneSideTransaction(transList$openTransaction, alignTime)
    closeTrans = AlignOneSideTransaction(transList$closeTransaction, alignTime)
    return(list(openTransaction = openTrans, closeTransaction = closeTrans))
}

GetOpenAndCloseTransaction = function(transaction) {
    cols = colnames(transaction)
    openCols= cols[startsWith(cols, "open")]
    closeCols= cols[startsWith(cols, "close")]
    sameCols = setdiff(cols, c(openCols, closeCols))
    openTransCols = c(openCols, sameCols)
    openTrans = transaction[, ..openTransCols]

    closeTransCols = c(closeCols, sameCols)
    closeTrans = transaction[, ..closeTransCols]

    return(list(openTransaction = openTrans, closeTransaction = closeTrans))
}

AlignOneSideTransaction = function(transaction, alignedTime) {
    timeCol = names(transaction)[1]
    transIndex = SelectIndex(transaction[[timeCol]], alignedTime)
    combinedIndex = split(seq_along(transIndex), transIndex)
    selectedTrans = rbindlist(GetCombinedTrans(transaction, combinedIndex))
    index = rep(NaN, length(alignedTime))
    index[unique(transIndex)] = 1:nrow(selectedTrans)
    oriAttr = attributes(selectedTrans)
    alignedTrans = as.list(selectedTrans)
    alignedTrans = lapply(alignedTrans, "[", index)
    alignedTrans[[timeCol]] = alignedTime
    setDT(alignedTrans)
    RecoverAttr(alignedTrans, oriAttr)
    return(alignedTrans)
}

GetCombinedTrans = function(transaction, combinedIndex) {
    lapply(combinedIndex, \(index) {
        trans = transaction[index]
        trans[1, qty := sum(trans$qty)]
        return(trans[1])
    })
}

CalcFactor = function(strategyLog, marketData, transaction) {
    x = 1
    return(x)
}

CalcY = function(strategyLog, marketData, transaction) {
    y = 1
    return(y)
}


StartTrain = function(task, x, y) {
    x = torch.randn(10, 10, 10)
    trainer = task$getTrainer()
    trainer$AddX(x)
    trainer$AddY(y)
    trainer$Train()
    saveData = trainer$GetSaveData()
}


getTrainer = function() {
    source_python("/home/<USER>/projects/Aegis/Prometheus/Trainer/LSTMTrainer.py")
    taskMetrics = list(
        inputSize = 10L,
        hiddenSize = 50L,
        outputSize = 1L,
        numLayers = 2L,
        epoch = 1L)
    a = LSTMTrainer("test", taskMetrics)
}

suppressMessages(library(Venus))
suppressMessages(library(Fortuna))

options(warn = 0)
options("scipen" = 10)
userRoot = "~"

logPath = GenLogPath(glue("{userRoot}/Log"), "FillModel")
InitLogger(logPath)

SetPreferredConcurrency()

InitDemeter()
InitCeres()

DefaultTimezone = Demeter::GetTimezone("ChinaFuture")


ds = GetFSTDataSource(glue("{userRoot}/Data/MarketData"))
logSource = GetFSTLogSource(glue("{userRoot}/Data/TradingLog"))
InitTradingSession(ds)

reportPath = "~/Report"
dataPath = "~/ReportData"
begin = MakeTime("2023-08-08 17:00:00")
end = MakeTime("2023-08-09 17:00:00")

server = "shftra"
strategy = "HFArb_agac"


logMsgType = c("OrderFill", "MD", "Arg", "TTOL")




task = NewTrainTask(
    begin = begin,
    end = end,
    server = server,
    strategy = strategy,
    dataSource = ds,
    logSource = logSource,
    logMsgType = logMsgType,
    getTrainer = getTrainer)

StartTrainTask(task)
