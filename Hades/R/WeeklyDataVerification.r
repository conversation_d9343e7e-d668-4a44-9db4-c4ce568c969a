#' Check gaps in solid data files
#' @description This function checks for abnormal time gaps in solid data files.
#' The core logic:
#' 1. For each trading day in the date range:
#'    - Read solid data file and calculate time differences between records
#'    - Detect gaps larger than threshold
#'    - For each gap, check if it occurs during trading session
#'    - Filter out gaps for T bonds on CFFEX exchange
#'    - Report gaps with details like position and size
#'
#' The function is mainly used to detect missing slots in market data, where an entire
#' time slot of data is missing. For example, if there should be data every second but
#' a 5-second slot is missing, this would be detected as a gap.
#'
#' @param marketType The market type to check, e.g. "ChinaFuture"
#' @param dataType The data type to check, e.g. "Tick"
#' @param gapThreshold The minimum gap size (in seconds) to report, default 1
#' @param begin The begin date to check, default NULL means check last date only
#' @param end The end date to check, default NULL means check last date only
#' @param verbose Whether to print detailed gap information, default TRUE
#' @return A data.table containing check results:
#'         - result: TRUE if no gaps found, FALSE otherwise
#'         - message: Description of gaps found or success message
#' @export
CheckSolidDataTimestampGap = function(marketType, dataType, exchange = NULL, gapThreshold = 1, begin = NULL, end = NULL, verbose = TRUE) {
    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    tradeOnWeekend = Demeter::GetTradeOnWeekends(marketType)
    tz = Demeter::GetTimezone(marketType)

    solidFileMgr = GetFileManagerEntry()$solid
    dataRoot = solidFileMgr$GetDataRoot(marketType, dataType)
    dates = solidFileMgr$ListDates(marketType, dataType, dataRoot, begin, end)
    if (is.null(begin) && is.null(end)) {
        dates = last(dates)
    }

    message = c()
    success = c()
    for (date in dates) {
        beginTime = GetBeginOfTradingDayNoDS(MakeTime(date), cuttingHour, cuttingHour)
        path = solidFileMgr$GetFilePath(marketType, dataType, date, NULL, dataRoot)
        data = GetSolidDataByPath(path)
        if (!is.null(exchange) && "exchange" %in% colnames(data)) {
            data = data[exchange == exchange]
        }
        data[, extTimestamp := StrToTime(timestamp, tz)]

        timeDiff = diff(data$extTimestamp)
        data[, gap := c(0, timeDiff)]
        abnormalIdx = which(timeDiff > gapThreshold) + 1

        abnormalResult = list()
        gap = c()

        if (!IsEmpty(abnormalIdx)) {
            for (idx in abnormalIdx) {
                abnormalData = data[idx]
                abnormalData[, preExtTimestamp := data$extTimestamp[idx - 1]]

                symbolExchange = FindExchange(marketType, abnormalData$symbol)
                if (symbolExchange == "CFFEX" && startsWith(abnormalData$symbol, "T")) next

                session = GetSession(beginTime, marketType, abnormalData$symbol)[[1]]
                inSession = abnormalData[, any(extTimestamp >= session$begin &
                                                   extTimestamp <= session$end &
                                                   preExtTimestamp >= session$begin &
                                                   preExtTimestamp <= session$end)]

                if (inSession) {
                    showIdx = (idx - 3):(idx + 2)
                    showIdx = showIdx[showIdx >= 1 & showIdx <= nrow(data)]
                    abnormalResult[[length(abnormalResult) + 1]] = data[showIdx]
                    gap = c(gap, abnormalData$gap)
                }
            }
        }

        exch = if (!is.null(exchange)) exchange else "all"
        if (!IsEmpty(abnormalResult)) {
            failMsg = glue("Gap detected: {date} has {length(abnormalResult)} gaps. max gap {max(gap)} at position {which.max(gap)} in {exch}")
            message = c(message, failMsg)
            success = c(success, FALSE)
            InfoLog("CheckSolidDataTimestampGap", failMsg)
            if (verbose) {
                for (i in seq_along(abnormalResult)) {
                    cat("Gap", i, ":", gap[i], "\n")
                    cat(DataTableToStr(abnormalResult[[i]]))
                    cat("\n")
                }
            }
        } else {
            successMsg = glue("No gap on {date} in {exch}")
            InfoLog("CheckSolidDataTimestampGap", successMsg)
            message = c(message, successMsg)
            success = c(success, TRUE)
        }
    }

    return(data.table(result = success, detailMessage = message))
}

#' Check for traffic jams in market data
#' @description This function monitors for traffic jams in market data, where multiple symbols have very small time gaps between updates.
#'
#' The core logic:
#' 1. For each trading day in the monitoring period:
#'    - Get all symbols traded on that day
#'    - For each symbol, check time gaps between consecutive updates
#'    - If gap is smaller than threshold, mark as potential traffic jam
#' 2. Group traffic jams by time to identify widespread issues:
#'    - Combine symbols with small gaps occurring at same time
#'    - Calculate duration and number of affected symbols
#'    - Report traffic jams affecting multiple symbols
#'
#' Traffic jams can be caused by:
#' - Network disconnection and subsequent data replay/resend
#' - Network congestion causing data to be buffered and sent in bursts
#'
#' @param dataSource The data source object to read market data
#' @param marketType The market type to monitor, e.g. "ChinaFuture"
#' @param dataType The data type to monitor, e.g. "Tick"
#' @param jamThreshold Threshold in seconds to identify traffic jams, default 0.1
#' @param begin Optional start time to monitor from
#' @param end Optional end time to monitor until
#' @return A data.table containing:
#'         - result: TRUE if no traffic jams found, FALSE otherwise
#'         - message: Description of any traffic jams found
#' @export
CheckDataTrafficJam = function(dataSource, marketType, dataType, jamThreshold = 0.1, countThreshold = 1, begin = NULL, end = NULL) {
    cuttingHour = Demeter:::GetTradingDayCuttingHour(marketType)
    tradeOnWeekend = Demeter:::GetTradeOnWeekends(marketType)
    tz = Demeter:::GetTimezone(marketType)

    dates = dataSource$ReadData(marketType, "Misc/TradingDay")$tradingday
    if (is.null(begin) && is.null(end)) {
        dates = last(dates)
    }
    if (!is.null(begin)) {
        begin = MakeTime(begin)
        dates = dates[which(dates >= begin)]
    }
    if (!is.null(end)) {
        end = MakeTime(end)
        dates = dates[which(dates <= end)]
    }

    dataInfo = dataSource$ListData(marketType, dataType)
    if (is.null(dataInfo)) {
        InfoLog("CheckDataTrafficJam", "Data info is empty. Nothing to do")
        return()
    }

    message = c()
    success = c()

    for (i in seq_along(dates)) {
        date = dates[i]
        trafficJamData = list()

        dateBegin = Demeter:::GetBeginOfTradingDayNoDS(date, cuttingHour, tradeOnWeekend)
        dateEnd = Demeter:::GetBeginOfNextTradingDayNoDS(date, cuttingHour, tradeOnWeekend)
        dateDataInfo = dataInfo[begin <= dateEnd & end >= dateBegin]

        symbols = dateDataInfo$symbol
        for (sym in symbols) {
            data = dataSource$ReadData(marketType, dataType, sym, dateBegin, dateEnd)
            data = FilterData(dataSource, data)
            if (IsEmpty(data)) next

            timeDiff = c(jamThreshold, diff(data$timestamp))
            jamIdx = which(timeDiff < jamThreshold) - 1
            if (IsEmpty(jamIdx)) next

            if (countThreshold <= 1) {
                jamIdx = jamIdx[c(1, which(diff(jamIdx) != 1) + 1)]
            } else {
                # Find the first value of the continuous sequence
                runs = rle(diff(jamIdx))
                startPos = cumsum(c(1, runs$lengths))
                keepPos = startPos[which(runs$values == 1 & runs$lengths > countThreshold - 2)]
                jamIdx = jamIdx[keepPos]
            }

            jamIdx = setdiff(jamIdx, 1)
            for (idx in jamIdx) {
                jamData = data[idx]
                jamData[, symbol := sym]
                jamData[, timeDiff := round(timeDiff[idx], 2)]
                trafficJamData = rbind(trafficJamData, jamData)
            }
        }

        if (!IsEmpty(trafficJamData)) {
            setorder(trafficJamData, timestamp)
            diffValue = as.numeric(diff(trafficJamData$timestamp, units = "secs"))
            threshold = 0.1
            trafficJamData[, group := cumsum(c(TRUE, diffValue > threshold))]
            tradiffJamGroup = split(trafficJamData, by = "group")
            jamTime = sapply(tradiffJamGroup, \(x) TimeToStr(first(x$timestamp), 0))
            jamSymbolCount = sapply(tradiffJamGroup, nrow)
            duration = sapply(tradiffJamGroup, \(x) x$timeDiff[1])
            failMsg = glue("Jam at {jamTime} affects {jamSymbolCount} symbols lasts {duration}")
            message = c(message, failMsg)
            success = c(success, FALSE)
            cat(DataTableToStr(trafficJamData))
        } else {
            successMsg = paste0("No traffic jam on ", DateToStr(date))
            message = c(message, successMsg)
            success = c(success, TRUE)
        }
    }

    return(data.table(result = success, message = message))
}

#' nodoc
#' @export
GetFlexibleCheckPeriod = function(marketType, begin = NULL, end = NULL) {
    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    cuttingMethod = Demeter::GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)
    tz = Demeter::GetTimezone(marketType)

    useDefaultEnd = is.null(end)
    end = if (!is.null(end)) MakeTime(end, tz) else GetBeginOfNextTradingDayNoDS(GetToday(), cuttingHour, tradeOnWeekends)
    begin = if (!is.null(begin)) MakeTime(begin, tz) else GetBeginOfTradingDayNoDS(end - days(1), cuttingHour, tradeOnWeekends)
    if (useDefaultEnd) end = end - milliseconds(1)
    return(list(begin = begin, end = end))
}

#' nodoc
#' @export
GetWeeklyCheckPeriod = function(marketType, from = GetToday()) {
    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    cuttingMethod = Demeter::GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)

    lastMonday = GetPrevWeekday(1, from)
    preLastMonday = GetPrevWeekday(1, lastMonday - days(1))
    begin = GetBeginOfTradingDayNoDS(preLastMonday, cuttingHour, tradeOnWeekends)
    end = GetBeginOfTradingDayNoDS(lastMonday, cuttingHour, tradeOnWeekends) - milliseconds(1)
    return(list(begin = begin, end = end))
}

#' nodoc
#' @export
CheckCFEFSTAndCSVDataEqualWeekly = function(taskArgs) {
    fstDataPath = "~/Data/MarketData"
    fstData = GetFSTDataSource(fstDataPath)
    csvDataPath = "~/RawData/MarketData/Standard"
    csvData = GetCSVDataSource(csvDataPath)

    marketType = "ChinaFuture"
    period = GetWeeklyCheckPeriod(marketType)
    begin = period$begin
    end = period$end

    cfTickResult = CheckDataEqual(marketType, "Tick", csvData, fstData, begin, end)
    cfOb5Result = CheckDataEqual(marketType, "Orderbook5", csvData, fstData, begin, end)

    coTickResult = CheckDataEqual("ChinaOption", "Tick", csvData, fstData, begin, end)
    coOb5Result = CheckDataEqual("ChinaOption", "Orderbook5", csvData, fstData, begin, end)

    return(CombineResult(cfTickResult, cfOb5Result, coTickResult, coOb5Result))
}

#' nodoc
#' @export
CheckSolidDataTimestampGapWeeklyCF = function(taskArgs) {
    fstDataPath = "~/Data/MarketData"
    fstData = GetFSTDataSource(fstDataPath)
    InitTradingSession(fstData)

    marketType = "ChinaFuture"
    dataType = "Tick"
    period = GetWeeklyCheckPeriod(marketType)
    begin = period$begin
    end = period$end

    result = CheckSolidDataTimestampGap(marketType, dataType, begin = begin, end = end)
    return(CombineResult(result))
}

#' nodoc
#' @export
CheckSolidDataTimestampGapWeeklyCS = function(taskArgs) {
    fstDataPath = "~/Data/MarketData"
    fstData = GetFSTDataSource(fstDataPath)
    InitTradingSession(fstData)

    marketType = "ChinaStock"
    dataType = "RemdIndexData"
    period = GetWeeklyCheckPeriod(marketType)
    begin = period$begin
    end = period$end

    allResult = c()
    exchThresholds = data.table(exchange = c("XSHE", "XSHG"), threshold = c(3, 3))
    for (i in seq_len(nrow(exchThresholds))) {
        exch = exchThresholds[i]
        result = CheckSolidDataTimestampGap(marketType, dataType, exchange = exch$exchange, gapThreshold = exch$threshold, begin = begin, end = end)
        allResult = c(allResult, list(result))
    }
    return(do.call(CombineResult, allResult))
}

#' nodoc
#' @export
CheckDataTrafficJamWeeklyCF = function(taskArgs) {
    fstDataPath = "~/Data/MarketData"
    fstData = GetFSTDataSource(fstDataPath)
    InitTradingSession(fstData)

    marketType = "ChinaFuture"
    dataType = "Tick"
    period = GetWeeklyCheckPeriod(marketType)
    begin = period$begin
    end = period$end

    result = CheckDataTrafficJam(fstData, marketType, dataType, begin = begin, end = end)
    return(CombineResult(result))
}

#' nodoc
#' @export
CheckDataTrafficJamWeeklyCS = function(taskArgs) {
    fstDataPath = "~/Data/MarketData"
    fstData = GetFSTDataSource(fstDataPath)
    InitTradingSession(fstData)

    marketType = "ChinaStock"
    dataType = "RemdIndexData"
    period = GetWeeklyCheckPeriod(marketType)
    begin = period$begin
    end = period$end

    result = CheckDataTrafficJam(fstData, marketType, dataType, countThreshold = 2, begin = begin, end = end)
    return(CombineResult(result))
}
