#' nodoc
#' @export
CheckDataDailyUpdate = function(dataSource, checkList, checkBeginDay = GetToday(), checkEndDay = checkBeginDay) {
    resV = c()
    msgV = c()

    for (i in seq_along(checkList)) {
        marketType = names(checkList)[i]
        dataTypes = checkList[[i]]

        tradingDays = dataSource$ReadData(marketType, "Misc/TradingDay", end = checkEndDay + days(20))
        begin = tradingDays[tradingday == checkBeginDay]$timestamp
        if (IsEmpty(begin)) {
            FatalLog("CheckDataDailyUpdate", "checkBeginDay is not trading day: {checkBeginDay}")
        }
        idx = which(tradingDays$tradingday == checkEndDay)
        end = tradingDays[idx + 1, timestamp]
        if (IsEmpty(end)) {
            FatalLog("CheckDataDailyUpdate", "checkEndDay is not trading day: {checkEndDay}")
        }

        dayStr = ifelse(checkBeginDay == checkEndDay, DateToStr(checkBeginDay), glue("[{checkBeginDay}, {checkEndDay}]"))
        for (dataType in dataTypes) {
            dataInfo = paste0(" on ", dayStr, " ", deparse(substitute(dataSource)), " source: ", dataSource$root, " ", marketType, "/", dataType)
            DoCheck = function() {
                InfoLog("CheckDataDailyUpdate", "Checking {marketType}/{dataType} DU @ {dayStr}")
                info = dataSource$ListData(marketType, dataType)
                if (is.null(info)) {
                    InfoLog("CheckDataDailyUpdate", "No data")
                    next
                }
                res = max(info$end) >= begin && min(info$begin) < end
                InfoLog("CheckDataDailyUpdate", "Result = {res}")
                data.table(res = res, msg = paste(ifelse(res, "Success:", "Failed:"), dataInfo))
            }
            e = try_capture_stack({
                value = DoCheck()
            }, environment())
            if (OnException(e, "Error in CheckDataDailyUpdate: {marketType}/{dataType}")) {
                value = data.table(res = FALSE, msg = paste("Failed:", e$message, dataInfo))
            }
            resV = c(resV, value$res)
            msgV = c(msgV, value$msg)
        }
    }
    return(data.table(result = resV, message = msgV))
}

CheckLogDailyUpdate = function(logSource, checkList) {
    resV = c()
    msgV = c()

    for (server in checkList) {
        tz = Ceres::GetTimezone(server)
        cuttingHour = Ceres::GetTradingDayCuttingHour(server)
        tradeOnWeekends = Ceres::GetTradeOnWeekends(server)
        begin = GetBeginOfTradingDayNoDS(GetToday(tz), cuttingHour, tradeOnWeekends)

        info = logSource$ListLog(server)
        lastTime = max(info$end)
        if (lastTime < begin) {
            resV = c(resV, FALSE)
            msgV = c(msgV, glue("Failed: {logSource$GetDesc()} {server}"))
        } else {
            resV = c(resV, TRUE)
            msgV = c(msgV, glue("Success: {logSource$GetDesc()} {server}"))
        }
    }
    return(data.table(result = resV, message = msgV))
}

CheckMiscDataEqual = function(marketType, dataType, src, dest, end = GetNow(), verbose = TRUE, srcData = NULL) {
    if (is.null(srcData)) {
        srcData = src$ReadData(marketType, dataType, end = end)
    }
    destData = dest$ReadData(marketType, dataType, end = end)

    srcName = src$GetDesc()
    destName = dest$GetDesc()
    result = isTRUE(all.equal(srcData, destData))
    resultStr = ifelse(result, "Success", "Failed")
    message = unclass(glue("{resultStr}: {marketType}/{dataType}, {srcName} & {destName}, T <= {TimeToStr(end)}"))

    if (!result && verbose) {
        srcDiffData = DataTableToStr(fsetdiff(srcData, destData))
        destDiffData = DataTableToStr(fsetdiff(destData, srcData))
        detailMessage = glue("
            {message}
            diff data in {srcName}
            {srcDiffData}
            -------------------------------------------------
            diff data in {destName}
            {destDiffData}",
            .null = ""
        )
        detailMessage = unclass(detailMessage)
    } else {
        detailMessage = message
    }

    return(data.table(result, message, detailMessage))
}

CheckDataEqual = function(marketType, dataType, recentDataSrc, fullDataSrc, begin = GetConfig(DefaultBegin), end = GetNow(), verbose = FALSE, symbols = c()) {
    b = begin
    e = end

    recentLsInfo = recentDataSrc$ListData(marketType, dataType)
    recentLsInfo = recentLsInfo[end >= b & begin <= e]

    fullLsInfo = fullDataSrc$ListData(marketType, dataType)
    fullLsInfo = fullLsInfo[end >= b & begin <= e]

    allSymbol = unique(c(recentLsInfo$symbol, fullLsInfo$symbol))
    if (!IsEmpty(symbols)) allSymbol = allSymbol[allSymbol %in% symbols]

    mktDT = glue("{marketType}/{dataType}")

    if (IsEmpty(allSymbol)) {
        return(data.table(result = TRUE, message = unclass(glue("Success: {mktDT} data is empty"))))
    }

    recentDsName = recentDataSrc$GetDesc()
    fullDsName = fullDataSrc$GetDesc()
    duration = glue("{TimeToStr(begin, nsmall = 0)} ~ {TimeToStr(end, nsmall = 0)}")

    failMessage = c()
    verboseMsg = c()
    failSym = c()
    symVerboseMsg = c()
    for (sym in allSymbol) {
        InfoLog("CheckDataEqual", "Comparing symbol: {mktDT}/{sym}")
        recentData = recentDataSrc$ReadData(marketType, dataType, sym, begin = begin, end = end)
        fullData = fullDataSrc$ReadData(marketType, dataType, sym, begin = begin, end = end)

        if (!isTRUE(all.equal(recentData, fullData))) {
            if (IsEmpty(fullData)) {
                symMsg = glue("{marketType}/{dataType}/{sym}: {nrow(recentData)}:0 rows")
                diffMsg = glue("{symMsg}
                    only {fullDsName} has data")
            } else if (IsEmpty(recentData)) {
                symMsg = glue("{marketType}/{dataType}/{sym}: 0:{nrow(fullData)} rows")
                diffMsg = glue("{symMsg}
                    only {recentDsName} has data")
            } else {
                recentDiffData = fsetdiff(recentData, fullData)
                fullDiffData = fsetdiff(fullData, recentData)
                symMsg = glue("{marketType}/{dataType}/{sym}: {nrow(recentDiffData)}:{nrow(fullDiffData)} rows")
                diffMsg = glue("
                    ----------------------------------------------------
                    {symMsg}
                    {recentDsName}
                    {DataTableToStr(recentDiffData)}

                    {fullDsName}
                    {DataTableToStr(fullDiffData)}",
                    .null = ""
                )
            }

            failSym = c(failSym, symMsg)
            if (verbose) {
                InfoLog("CheckDataEqual", symMsg)
                symVerboseMsg = c(symVerboseMsg, diffMsg)
            }
        }
    }

    if (IsEmpty(failSym)) {
        message = unclass(glue("Success: {mktDT} are equal in time {duration}"))
        return(data.table(result = TRUE, message))
    }

    message = paste0(glue("duration: {duration}"), "\n", paste0(failSym, collapse = "\n"))

    if (verbose) {
        detailFailSymMsg = paste0(symVerboseMsg, collapse = "\n")
        detailMessage = paste0(message, "\n", "Detail fail info: ", "\n", detailFailSymMsg)
    } else {
        detailMessage = message
    }

    return(data.table(result = FALSE, message, detailMessage))
}

#' nodoc
#' @export
CheckCFEDailyUpdate = function(taskArgs) {
    targetDate = GetFirstOK(StrToDate(taskArgs$targetDate), GetToday())
    SetStateVar("DU.CFE.HT", "Doing", targetDate)
    result = WaitForSignal("DU.CFE", targetDate, timeout = minutes(90))
    if (result$state != "Done") {
        SetStateVar("DU.CFE.HT", result$state, targetDate)
        return(ConvertResult(result))
    }

    fstDataPath = "~/Data/MarketData"
    fstData = GetFSTDataSource(fstDataPath)

    checkList = list()
    marketTypes = c("ChinaFuture", "ChinaOption")

    for (mkt in marketTypes) {
        checkList[[mkt]] = c("Tick", "Orderbook5", "Best", "Deep", "MatchPriceQty", "OrderStatistic", "TenEntrust")
    }

    csvDataPath = "~/RawData/MarketData/Standard"
    csvData = GetCSVDataSource(csvDataPath)
    csvResult = CheckDataDailyUpdate(csvData, checkList, targetDate)

    nDaysReadBack = 5
    cfGapResult = CheckTimestampGap(
        fstData, "ChinaFuture", checkList[["ChinaFuture"]],
        targetDate, nDaysReadBack
    )

    checkList[["ChinaFuture"]] = c(checkList[["ChinaFuture"]], "Misc/PxLimit", "Bar/60")
    fstResult = CheckDataDailyUpdate(fstData, checkList, targetDate)

    res = CombineResult(fstResult, csvResult, cfGapResult)
    SetStateVar("DU.CFE.HT", ifelse(res$result, "Done", "Failed"), targetDate)
    return(res)
}

#' nodoc
#' @export
CheckCSEDailyUpdate = function(taskArgs) {
    targetDate = GetFirstOK(StrToDate(taskArgs$targetDate), GetToday())
    SetStateVar("DU.CSE.HT", "Doing", targetDate)
    result = WaitForSignal("DU.CSE", targetDate, timeout = hours(3))
    if (result$state != "Done") {
        SetStateVar("DU.CSE.HT", result$state, targetDate)
        return(ConvertResult(result))
    }

    checkList = list()
    marketTypes = c("ChinaCvtBond", "ChinaPlgBond", "ChinaStock", "ChinaETF")

    for (mkt in marketTypes) {
        checkList[[mkt]] = c("RemdOrderAction", "RemdOrderFill", "RemdOrderbook10")
    }
    checkList[["ChinaCvtBond"]] = c(checkList[["ChinaCvtBond"]], "RemdOrderActionSH")
    checkList[["ChinaPlgBond"]] = c(checkList[["ChinaPlgBond"]], "RemdOrderActionSH")
    checkList[["ChinaStock"]] = c(checkList[["ChinaStock"]], c("RemdSpotTick", "RemdIndexData"))
    checkList[["ChinaETF"]] = c(checkList[["ChinaETF"]], "RemdSpotTick")

    csvData = GetCSVDataSource("~/RawData/MarketData/Standard")
    csvResult = CheckDataDailyUpdate(csvData, checkList, targetDate)

    fstData = GetFSTDataSource("~/Data/MarketData")
    nDaysReadBack = 0
    gapResult = c()
    for (mkt in marketTypes) {
        gapResult = c(gapResult, list(CheckTimestampGap(
            fstData, mkt, checkList[[mkt]],
            targetDate, nDaysReadBack
        )))
    }

    checkList[["ChinaStock"]] = c(checkList[["ChinaStock"]], "Bar/60")
    checkList[["ChinaETF"]] = c(checkList[["ChinaETF"]], "Bar/60")
    fstResult = CheckDataDailyUpdate(fstData, checkList, targetDate)

    checkList = list("ChinaStock" = "Misc/PxLimit")
    checkBeginDay = AddTradingDays(targetDate, -1, fstData, "ChinaStock")
    pxlimitResult = CheckDataDailyUpdate(fstData, checkList, checkBeginDay, targetDate)

    checkList = list("ChinaStock" = "Misc/PxAdj")
    checkBeginDay = AddTradingDays(targetDate, -4, fstData, "ChinaStock")
    pxAdjResult = CheckDataDailyUpdate(fstData, checkList, checkBeginDay, targetDate)

    res = do.call(CombineResult, c(list(csvResult, fstResult, pxAdjResult, pxlimitResult), gapResult))
    SetStateVar("DU.CSE.HT", ifelse(res$result, "Done", "Failed"), targetDate)
    return(res)
}

#' nodoc
#' @export
CheckTradingLogDailyUpdate = function(taskArgs) {
    targetDate = StrToDate(taskArgs$targetDate)
    targetDate = GetFirstOK(targetDate, GetToday())

    signal = taskArgs$signal
    AssertNotNull(signal)
    result = WaitForSignal(signal, targetDate, timeout = minutes(30))
    if (result$state != "Done") return(ConvertResult(result))

    InitCeres()
    logDataPath = "~/Data/TradingLog"
    logData = GetFSTLogSource(logDataPath)

    AssertNotNull(taskArgs$serverList)
    checkList = unlist(strsplit(taskArgs$serverList, ","))
    logResult = CheckLogDailyUpdate(logData, checkList)
    return(CombineResult(logResult))
}

#' nodoc
#' @export
CheckTimestampGap = function(dataSource, marketType, dataType, targetDate, nDaysReadBack) {
    result = TRUE
    detailMessage = ""
    exceptionData = data.table()
    for (dt in dataType) {
        InfoLog("CheckTimestampGap", "Checking {marketType}/{dt} timestamp gap")
        gapResult = CheckClockSkew(
            dataSource, marketType, dt,
            targetDate, nDaysReadBack
        )
        for (exch in names(gapResult)) {
            data = gapResult[[exch]]
            detailMessage = glue("{detailMessage}\n{dt}/{exch}:\n{DataTableToStr(data)}")
            if (last(data$Status) == "Exception") {
                result = FALSE
                data[, dataType := dt]
                setcolorder(data, c(1, ncol(data), 2:(ncol(data) - 1)))
                exceptionData = rbind(exceptionData, tail(data, 1))
            }
        }
    }
    message = ifelse(result, "", glue("Timestamp gap exception:\n{DataTableToStr(exceptionData)}"))

    return(data.table(result = result, message = as.character(message), detailMessage = as.character(detailMessage)))
}

#' Monitor timestamp gap (clock skew) between exchtime and timestamp
#' @description This function monitors the time gap (clock skew) between exchange time and timestamp for market data.
#' It calculates the distribution of time gaps in different intervals (0-10s, 10s-60s, 1m-5m, >5m)
#' and detects abnormal patterns using 2-sigma rule.
#'
#' The core logic:
#' 1. For each trading day in the lookback period:
#'    - Get all symbols traded on that day
#'    - Calculate time difference between exchtime and timestamp for each symbol
#'    - Count number of records falling into each gap interval
#' 2. Group results by exchange and detect abnormal patterns if:
#'    - Count exceeds mean +/- 2 standard deviations
#'    - Deviation from threshold exceeds 1000 records
#'
#' @param dataSource The data source object to read market data
#' @param marketType The market type, e.g. "ChinaFuture"
#' @param dataType The data type to monitor, e.g. "Tick"
#' @param endDate The end date to monitor
#' @param nDaysReadBack Number of historical days to look back
#' @return A list of data.tables grouped by exchange, containing:
#'         - timestamp: Trading day
#'         - exchange: Exchange name
#'         - Count of records in each gap interval
#'         - Status: "Normal" or "Exception"
#' @export
CheckClockSkew = function(dataSource, marketType, dataType, endDate, nDaysReadBack) {
    tz = Demeter::GetTimezone(marketType)
    endDate = MakeTime(endDate, tz)
    listInfo = dataSource$ListData(marketType, dataType)

    cuttingHour = Demeter::GetTradingDayCuttingHour(marketType)
    cuttingMethod = Demeter::GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = Demeter::GetTradeOnWeekends(marketType)

    tradingDay = GetTradingDayNoDS(endDate, cuttingHour, cuttingMethod, tradeOnWeekends)

    gapVec = c(0, 10, 60, 300, Inf)
    gapType = c("0s ~ 10s", "10s ~ 60s", "1m ~ 5m", ">5m")
    totalTable = c()

    for (nday in nDaysReadBack:0) {
        targetDay = AddTradingDays(tradingDay, - nday, dataSource, marketType)
        InfoLog("GetTimestampGap", "Getting timestamp gap of trading day: ", DateToStr(targetDay))
        b = GetBeginOfTradingDayNoDS(targetDay, cuttingHour, tradeOnWeekends)
        e = GetBeginOfNextTradingDayNoDS(targetDay, cuttingHour, tradeOnWeekends)
        checkSymbol = listInfo[begin <= e & end >= b]$symbol
        dayTable = c()

        for (sym in checkSymbol) {
            symData = dataSource$ReadData(marketType, dataType, sym, b, e)
            if (IsEmpty(symData)) next
            timeDiff = as.numeric(abs(symData[, difftime(timestamp, exchtime, units = "secs")]))

            symTable = data.table()
            for (i in seq_along(gapType)) {
                gapIdx = which(timeDiff >= gapVec[i] & timeDiff < gapVec[i + 1])
                count = length(gapIdx)
                symTable = cbind(symTable, count = count)
                colnames(symTable)[i] = gapType[i]
            }
            symTable = cbind(symbol = sym, symTable)
            dayTable = rbind(dayTable, symTable)
        }

        if (!IsEmpty(dayTable)) {
            dayTable[, exchange := FindExchange(marketType, symbol)]
            dayTable = dayTable[, .(c1 = sum(get(gapType[1])),
                                    c2 = sum(get(gapType[2])),
                                    c3 = sum(get(gapType[3])),
                                    c4 = sum(get(gapType[4]))),
                                    by = c("exchange")]
            colnames(dayTable)[2:5] = gapType
            dayTable = cbind(timestamp = targetDay, dayTable)
            totalTable = rbind(totalTable, dayTable)
        }
    }

    setorder(totalTable, "exchange", "timestamp")
    exchTable = split(totalTable, by = c("exchange"))

    for (exch in names(exchTable)) {
        data = exchTable[[exch]]
        status = "Normal"
        # do 2-sigma test for count value
        for (gap in gapType) {
            countMean = mean(data[[gap]])
            countSd = GetFirstOK(sd(data[[gap]]), 0)
            low = countMean - 2 * countSd
            high = countMean + 2 * countSd
            beyond2sigma = data[[gap]] > high | data[[gap]] < low
            beyondCount = abs(data[[gap]] - high) > 1000 | abs(data[[gap]] - low) > 1000
            status = ifelse(beyond2sigma & beyondCount, "Exception", status)
        }
        data[, Status := status]
    }

    return(exchTable)
}

#' nodoc
#' @export
CheckCFSessionUnchanged = function(taskArgs) {
    targetDate = StrToDate(taskArgs$targetDate)
    targetDate = GetFirstOK(targetDate, GetToday())

    result = WaitForSignal("DU.CFE", targetDate, timeout = minutes(90))
    if (result$state != "Done") return(ConvertResult(result))

    fstDataPath = "~/Data/MarketData"
    fstData = GetFSTDataSource(fstDataPath)
    chgResult = MonitorSessionChg(fstData, targetDate)
    unchanged = is.null(chgResult)
    if (unchanged) {
        message = ""
    } else {
        message = DataTableToStr(chgResult)
    }
    return(list(result = unchanged, content = message))
}

#' nodoc
#' @export
CheckCryptoDailyUpdate = function(taskArgs) {
    NotImplemented()
    targetDate = StrToDate(taskArgs$targetDate)
    targetDate = GetFirstOK(targetDate, GetToday())

    bi = "Crypto"
    DefaultTimezone = Demeter::GetTimezone(bi)

    result = WaitForSignal("DU.Crypto", targetDate)
    if (result$state != "Done") return(ConvertResult(result))

    fstDataPath = "~/Data/MarketData"
    fstData = GetFSTDataSource(fstDataPath)

    checkList = list()

    checkList[[bi]] = c("BNBookTicker", "TBDepth", "TBTrade")

    fstResult = CheckDataDailyUpdate(fstData, checkList, targetDate)

    csvDataPath = "~/RawData/MarketData/Standard"
    csvData = GetCSVDataSource(csvDataPath)
    csvResult = CheckDataDailyUpdate(csvData, checkList, targetDate)

    return(CombineResult(fstResult, csvResult))
}

#' nodoc
#' @export
CheckCFCrawledSettlement = function(taskArgs) {
    targetDate = StrToDate(taskArgs$targetDate)
    targetDate = GetFirstOK(targetDate, GetToday())

    result = WaitForSignal("DU.CFE", targetDate, timeout = minutes(90))
    if (result$state != "Done") return(ConvertResult(result))

    fstDataPath = "~/Data/MarketData"
    fstData = GetFSTDataSource(fstDataPath)
    exchRawPath = "~/RawData/MarketData/ExchRaw/ChinaFuture/Settlement"
    checkDataPath = file.path(exchRawPath, DateToStr(targetDate))
    crawedExch = file_path_sans_ext(list.files(checkDataPath))
    exch = c("shfe", "czce", "dce")
    result = all(exch %in% crawedExch)
    if (!result) {
        crawedExch = ifelse(length(crawedExch) == 0, "Empty", toString(crawedExch))
        message = paste0("Crawled exchange is ", crawedExch)
    } else {
        message = "Success to crawl exch raw data"
    }

    return(list(result = result, content = message))
}
