<?xml version="1.0" encoding="utf-8"?>
<Hades>
  <Senders>
    <Sender name="DataNotifier">
      <WeChat account="AegisNotifier"/>
    </Sender>
  </Senders>
  <Receivers>
    <Receiver name="DataWatcher">
      <WeChat contact="Aegis-InfraMon"/>
    </Receiver>
  </Receivers>
  <TaskInfo>
    <TaskGroup name="CFE" sender="DataNotifier" receiver="DataWatcher">
      <Task name="ChinaFuture Session Change" func="CheckCFSessionUnchanged" alwaysNotify="FALSE"/>
      <Task name="ChinaFutureExch Data Daily Update" func="CheckCFEDailyUpdate" alwaysNotify="TRUE"/>
      <Task name="ChinaFutureExch Log Daily Update" func="CheckTradingLogDailyUpdate" alwaysNotify="TRUE" signal="DU.CFE.Log" serverList="shftra,shftrb,dltra,shgtra,shgtrb"/>
    </TaskGroup>
    <TaskGroup name="CSE" sender="DataNotifier" receiver="DataWatcher">
      <Task name="ChinaStockExch Data Daily Update" func="CheckCSEDailyUpdate" alwaysNotify="TRUE"/>
    </TaskGroup>
    <TaskGroup name="CFE.Crawl" sender="DataNotifier" receiver="DataWatcher">
      <Task name="ChinaFuture Daily Crawl Exch Settlement" func="CheckCFCrawledSettlement" alwaysNotify="FALSE"/>
    </TaskGroup>
    <TaskGroup name="CFE.Weekly" sender="DataNotifier" receiver="DataWatcher">
      <Task name="ChinaFutureExch Data Week Check" func="CheckCFEFSTAndCSVDataEqualWeekly" alwaysNotify="TRUE"/>
    </TaskGroup>
    <TaskGroup name="FlexibleCrossCompare" sender="DataNotifier" receiver="DataWatcher">
      <Task name="Flexible Cross Compare with dataServer" func="FlexibleCrossCompare" alwaysNotify="TRUE" compareServer="" marketType="" dataType="" symbol="" begin="" end=""/>
    </TaskGroup>
  </TaskInfo>
</Hades>
