#==================================================================================================
# utilities
CastLogData = function(logData, tz) {
    columnType = attr(logData, "columnType")
    AssertNotNull(columnType)
    Assert(length(columnType) == ncol(logData))

    timeCols = colnames(logData)[columnType == "time"]
    if (length(timeCols) > 0)
        logData[, (timeCols) := lapply(.SD, function(x) StrToTime(x, tz)), .SDcols = timeCols]

    numericCols = colnames(logData)[columnType == "numeric"]
    if (length(numericCols) > 0)
        logData[, (numericCols) := lapply(.SD, as.numeric), .SDcols = numericCols]

    return(logData)
}

UpdateServerLog = function(serverName, data, logSource, splitString = "Logger") {
    validLogData = list()
    for (msgType in names(data)) {
        l = split(data[[msgType]], by = splitString)
        for (component in names(l)) {
            d = l[[component]]
            tz = Ceres::GetTimezone(serverName)
            d = CastLogData(d, tz)

            if (is.unsorted(d$Timestamp)) {
                invalidTime = d$Timestamp[which(diff(d$Timestamp) < 0)]
                WarningLog("UpdateServerLog", "Log data {serverName}/{component}/{msgType} has unsorted time: {invalidTime}")
                return(FALSE)
            }

            setattr(d, "component", component)
            setattr(d, "msgType", msgType)
            setattr(d, "serverName", serverName)
            validLogData[[length(validLogData) + 1]] = d
        }
    }

    for (logData in validLogData) {
        serverName = GetServerName(logData)
        component = GetComponent(logData)
        msgType = GetMsgType(logData)
        InfoLog("UpdateServerLog", "Writing log data: {serverName}/{component}/{msgType}")
        logSource$WriteLog(logData)
    }

    return(TRUE)
}

#==================================================================================================

#' nodoc
#' @export
ParseLog = function(rawLogRoot, logSource, servers = NULL,
    begin = GetConfig(DefaultBegin), end = GetConfig(DefaultEnd),
    configRoot = GetConfig(DefaultConfigRoot), interests = NULL,
    verbose = FALSE, nThreads = GetPreferredConcurrency()) {

    InfoLog("ParseLog", "Begin parsing Log {rawLogRoot} => {logSource$GetDesc()}")

    files = file.path(configRoot, "LogDef")
    defsPath = GetFirstExistingFile(files)
    defs = list.files(defsPath, full.names = TRUE, recursive = FALSE)
    defs = c(GetCommonLogDef(), defs)

    if (!IsEmpty(servers)) {
        serverDirs = servers
    } else {
        serverDirs = list.dirs(rawLogRoot, full.names = FALSE, recursive = FALSE)
    }

    failServer = c()
    manualFill = "ManualFill"
    for (server in serverDirs) {
        message("==============================")
        InfoLog("ParseLog", "Server: {server}")

        cuttingHour = GetTradingDayCuttingHour(server)
        cuttingMethod = GetTradingDayCuttingMethod(server)
        tradeOnWeekends = GetTradeOnWeekends(server)

        b = GetTradingDayNoDS(MakeTime(begin), cuttingHour, cuttingMethod, tradeOnWeekends)
        e = GetTradingDayNoDS(MakeTime(end), cuttingHour, cuttingMethod, tradeOnWeekends)

        itsPeriod = GetServerInterestsPeriod(server, interests, logSource)

        if (is.null(interests)) {
            manualFillPeriod = itsPeriod
        } else if (!IsEmpty(itsPeriod[[manualFill]])) {
            manualFillPeriod = itsPeriod[[manualFill]]
            itsPeriod[[manualFill]] = NULL
        } else {
            manualFillPeriod = NULL
        }

        serverPath = file.path(rawLogRoot, server)
        dayDirs = list.dirs(serverPath, full.names = FALSE, recursive = FALSE)

        for (dayDir in dayDirs) {
            day = StrToDate(dayDir)

            if (day < b || day > e) next
            # step 1: parse all.log
            checkResult = CheckDayParseInterests(day, interests, itsPeriod)
            if (checkResult$doParse) {
                updated = ParseServerInterests(
                    server, serverPath, day,
                    checkResult$parseIts, logSource, defs, verbose, nThreads
                )
                # stop updating subsequent server log if failed
                if (!updated) {
                    failServer = c(failServer, server)
                    break
                }
            }

            # step 2: parse manual fill
            if (is.null(manualFillPeriod)) next
            if (day <= manualFillPeriod$parsedEnd) {
                next
            }

            ParseServerManualFill(server, serverPath, day, logSource)
        }
    }

    InfoLog("ParseLog", "End parsing Log {rawLogRoot} => {logSource$GetDesc()}")
    return(failServer)
}

GetServerInterestsPeriod = function(server, interests, logSource) {
    cuttingHour = GetTradingDayCuttingHour(server)
    cuttingMethod = GetTradingDayCuttingMethod(server)
    tradeOnWeekend = GetTradeOnWeekends(server)

    listInfo = logSource$ListLog(server)

    if (IsEmpty(listInfo)) {
        parsedBegin = GetNow()
        parsedEnd = 0
        existIts = list()
    } else {
        parsedBegin = GetTradingDayNoDS(min(listInfo$begin),
                cuttingHour, cuttingMethod, tradeOnWeekend)
        parsedEnd = GetTradingDayNoDS(max(listInfo$end),
                cuttingHour, cuttingMethod, tradeOnWeekend)

        listInfo[, symbol := gsub(".*\\.", "", symbol)]
        existIts = split(listInfo, by = "symbol")
    }

    itsPeriod = list()
    if (!is.null(interests)) {
        for (its in interests) {
            if (is.null(existIts[[its]])) {
                itsPeriod[[its]] = list(parsedBegin = GetNow(), parsedEnd = 0)
            } else {
                iParsedBegin = GetTradingDayNoDS(min(existIts[[its]]$begin),
                        cuttingHour, cuttingMethod, tradeOnWeekend)
                iParsedEnd = GetTradingDayNoDS(max(existIts[[its]]$end),
                        cuttingHour, cuttingMethod, tradeOnWeekend)

                itsPeriod[[its]] = list(parsedBegin = iParsedBegin,
                                         parsedEnd = iParsedEnd)
            }
        }
    }

    if (IsEmpty(itsPeriod))
        return(list(parsedBegin = parsedBegin, parsedEnd = parsedEnd))
    else
        return(itsPeriod)
}

CheckDayParseInterests = function(day, interests, interestsPeriod) {
    parseIts = character()
    doParse = TRUE
    if (is.null(interests)) {
        if (day <= interestsPeriod$parsedEnd) {
            TraceLog("CheckDayParseInterests", "Skip log data: {day}")
            doParse = FALSE
        }
    } else {
        for (its in names(interestsPeriod)) {
            if (day <= interestsPeriod[[its]]$parsedEnd) {
                TraceLog("CheckDayParseInterests", "Skip Interest data: {its} in {day}")
                next
            }
            parseIts = c(parseIts, its)
        }
        if (length(parseIts) == 0) doParse = FALSE
    }
    return(list(doParse = doParse, parseIts = parseIts))
}

ParseServerInterests = function(server, serverPath, day, parseInterests,
    logSource, defs, verbose, nThreads) {

    InfoLog("ParseServerInterests", "Parsing all.log: {day}")
    logFileName = "all.log"
    logPath = file.path(serverPath, day, logFileName)
    data = ParseText(defs, parseInterests, files = logPath,
        verbose = verbose, nThreads = nThreads)
    updated = UpdateServerLog(server, data, logSource)
    rm(data)
    gc()
    return(updated)
}

ParseServerManualFill = function(server, serverPath, day, logSource) {
    manualInterest = "ManualFill"
    logFileName = paste0("ManualFill", ".csv")
    logPath = file.path(serverPath, day, logFileName)
    if (!file.exists(logPath)) return(TRUE)
    InfoLog("ParseServerManualFill", "Parsing ManualFill.csv: {day}")
    data = fread(logPath, colClasses = "character", blank.lines.skip = TRUE)
    setattr(data, "columnType", c("time", rep("string", 4), rep("numeric", 3)))
    data = list(ManualFill = data)
    updated = UpdateServerLog(server, data, logSource, splitString = "stg")
    return(updated)
}