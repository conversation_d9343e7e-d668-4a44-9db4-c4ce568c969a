#' nodoc
#' @export
ArchiveLog = function(logRoot, archiveRoot, server, compression = "xz", fullScan = FALSE) {
    return(ArchiveData(logRoot, archiveRoot, "", server, compression, fullScan))
}

#' nodoc
#' @export
RestoreLog = function(archiveRoot, logRoot, server, compression = "xz", begin = NULL) {
    return(RestoreFromArchive(archiveRoot, logRoot, "", server, compression, begin))
}

#' add Exchange column to src(old log may without this Column) and output to dest
#'
#' @param src: the source data
#' @param dest: the dest data
#' @param server: the server whose log needs to be fixed
#' @export
FixChinaFutureLogData = function(src, dest, server) {
    if (identical(src, dest)) {
        FatalLog("FixChinaFutureLogData", "src cannot be equal to dest")
    }

    AddExchangeToLog = function(log) {
        if (IsEmpty(log)) return(log)
        hasExchange = "Exchange" %in% colnames(log)
        hasSymbol = "Symbol" %in% colnames(log)
        server = GetServerName(log)
        component = GetComponent(log)
        msgType = GetMsgType(log)

        if (!hasSymbol) return(log)
        if (msgType == "MD") return(log)
        if (hasExchange && !any(is.na(log$Exchange)) && !any(log$Exchange == "")) return(log)

        InfoLog("FixChinaFutureLogData", "Add exchange to {server}/{component}/{msgType}")
        log[, Exchange := FindExchange("ChinaFuture", Symbol)]
        return(log)
    }

    CopyLog(server, src, dest, TransformFunc = AddExchangeToLog)
}

#' nodoc
#' @export
CompareLogSource = function(src, dest, outputPath) {
    serverList = src$ListLog()
    for (server in serverList) {
        serverInfo = src$ListLog(server)
        for (i in 1:nrow(serverInfo)) {
            InfoLog("CompareLogSource", "Check {server}/{symbol}")
            symbol = serverInfo$symbol[i]
            component = serverInfo$component[i]
            msgType = serverInfo$msgType[i]

            begin = serverInfo[i]$begin
            end = serverInfo[i]$end

            srcData = src$ReadLog(server, component, msgType, begin, end)
            destData = dest$ReadLog(server, component, msgType, begin, end)

            udata = NULL
            if (IsEmpty(srcData)) {
                udata = destData
            } else {
                allData = rbind(srcData, destData)
                uid = tail(c(1:nrow(allData))[-which(duplicated(allData))], - nrow(srcData))
                udata = allData[uid]
            }

            if (!IsEmpty(udata)) {
                udata[, Timestamp := TimeToStr(Timestamp)]
                fileName = paste0(server, ".", component, ".", msgType, ".csv")
                fwrite(udata, file.path(outputPath, fileName), na = "NA")
            }
        }
    }
}

#' nodoc
#' @export
GetBeginEndOfTradingDay = function(server, begin, end) {
    cuttingHour = GetTradingDayCuttingHour(server)
    tradeOnWeekends = GetTradeOnWeekends(server)
    begin = GetBeginOfTradingDayNoDS(begin, cuttingHour, tradeOnWeekends)
    end = GetBeginOfNextTradingDayNoDS(end, cuttingHour, tradeOnWeekends)
    return(list(begin = begin, end = end))
}

#' nodoc
#' @export
GetTraderInSpecialList = function(time, exchange, trader) {
    specList = SpecialTraderList
    if (is.null(specList)) {
        WarningLog("GetTraderInSpecialList", "Special trader list is not available")
        return(rep(FALSE, length(time)))
    }

    Assert(length(time) == length(exchange) & length(exchange) == length(trader))
    e = exchange
    t = trader
    res = sapply(1:length(time), \(i) {
        resultList = specList[exchange == e[i] & trader == t[i] & timestamp <= time[i]]
        if (IsEmpty(resultList)) {
            return(FALSE)
        }
        return(last(resultList)$action == "in")
    })
    return(res)
}