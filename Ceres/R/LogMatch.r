#' nodoc
#' @export
GetResetTime = function(strategyLog) {
    okLog = GetFirstOK(strategyLog$SendingOrder, strategyLog$Exec)
    return(okLog[OrderId == 0, Timestamp])
}

#' nodoc
#' @export
FindMatchMD = function(mdA, mdB) {
    mdA[, aIndex := 1:.N]
    mdB[, bIndex := 1:.N]

    server = GetServerName(mdA)
    cuttingHour = GetTradingDayCuttingHour(server)
    cuttingMethod = GetTradingDayCuttingMethod(server)
    tradeOnWeekends = GetTradeOnWeekends(server)

    mdA[, day := GetTradingDayNoDS(mdA$Timestamp, cuttingHour, cuttingMethod, tradeOnWeekends)]
    mdB[, day := GetTradingDayNoDS(mdB$Timestamp, cuttingHour, cuttingMethod, tradeOnWeekends)]
    allTd = unique(c(mdA$day, mdB$day))
    matchResult = rbindlist(lapply(allTd, \(td) {
        MatchIntradayMD(mdA[day == td], mdB[day == td])
    }))

    mdA[, aIndex := NULL]
    mdB[, bIndex := NULL]
    return(matchResult)
}

MatchIntradayMD = function(mdA, mdB) {
    if (IsEmpty(mdA) || IsEmpty(mdB)) {
        return(NULL)
    }

    checkCol = c("Symbol", "AskPx", "AskVol", "BidPx", "BidVol", "LastPx", "OpenInterest", "TotalVol", "TotalValue")
    matchResult = mdA[mdB, on = checkCol, allow.cartesian = TRUE]
    matchResult = matchResult[!is.na(aIndex)]

    matchResult[, timeDiff := as.numeric(difftime(i.Timestamp, Timestamp, unit = "secs"))]

    matchResult[, aIndexSingle := !aIndex %in% aIndex[duplicated(aIndex)]]
    matchResult[, bIndexSingle := !bIndex %in% bIndex[duplicated(bIndex)]]
    matchResult[, single := aIndexSingle & bIndexSingle]

    boxStat = boxplot.stats(matchResult[single == TRUE]$timeDiff, coef = 3)
    timeDiffLower = boxStat$stats[1]
    med = boxStat$stat[3]
    timeDiffUpper = boxStat$stats[5]

    matchResult = matchResult[timeDiff >= timeDiffLower & timeDiff <= timeDiffUpper]
    matchResult[, delta := abs(timeDiff - med)]
    setorder(matchResult, bIndex, delta)
    matchResult = matchResult[, .SD[1], by = bIndex] # minest delta matched
    setorder(matchResult, aIndex, delta)
    matchResult = matchResult[, .SD[1], by = aIndex] # minest delta matched

    return(matchResult[, .(aIndex, bIndex, timeDiff)])
}

#' nodoc
#' @export
FindMatchTTOLMDOrder = function(ttolLog, mdLog, orderLog) {

    # check the seding/xing & ttol matching type
    msgType = GetMsgType(orderLog)
    if (msgType == "SendingOrder") {
        Assert(identical(unique(ttolLog$ActionType), "Send"))
    } else if (msgType == "XingOrder") {
        Assert(identical(unique(ttolLog$ActionType), "Cancel"))
    }

    symbol = sort(unique(orderLog$Symbol))

    if (IsEmpty(orderLog) ||
        IsEmpty(mdLog) ||
        IsEmpty(ttolLog)) {
        return(NULL)
    }

    ret = list()
    for (sym in symbol) {
        symOrderIndex = which(orderLog$Symbol == sym)
        symOrderTime = orderLog$Timestamp[symOrderIndex]
        mdTime = mdLog$Timestamp

        ttolOrderMatch = FindMatch(
            ttolLog, orderLog[symOrderIndex],
            "TriggerTime", "Timestamp", 0.1
        )

        if (IsEmpty(ttolOrderMatch)) next

        matchedTTOLIdx = ttolOrderMatch$aIndex
        matchedOrderIdx = ttolOrderMatch$bIndex

        matchedMDIdx = findInterval(symOrderTime[matchedOrderIdx], mdTime)

        invalidMatch = which(matchedMDIdx == 0)
        if (length(invalidMatch) > 0) {
            matchedMDIdx = matchedMDIdx[-invalidMatch]
            matchedOrderIdx = matchedOrderIdx[-invalidMatch]
        }

        orderMDTimeDiff = difftime(symOrderTime[matchedOrderIdx],
            mdTime[matchedMDIdx],
            units = ("secs")
        )

        # 01sec timediff for order and md
        validIdx = which(orderMDTimeDiff < 0.1)

        if (length(validIdx) == 0) next

        ttolIdx = matchedTTOLIdx[validIdx]
        orderIdx = symOrderIndex[matchedOrderIdx][validIdx]
        mdIdx = matchedMDIdx[validIdx]

        ret[[sym]] = data.table(
            ttolIdx = ttolIdx,
            mdIdx = mdIdx,
            orderIdx = orderIdx
        )
    }

    return(ret)
}
